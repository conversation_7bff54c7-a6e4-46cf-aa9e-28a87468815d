#!/bin/bash

echo "🚀 === GPU加速启动 TurtleBot3 SLAM 3D Demo ==="
echo ""

# 设置工作目录
cd /root/autodl-tmp/rtab_ws
source devel/setup.bash

# 加载GPU环境变量
echo "🔧 加载GPU加速环境..."
source gpu_env.sh

# 验证GPU配置
echo "📊 验证GPU配置:"
echo "OpenGL渲染器: $(glxinfo | grep 'OpenGL renderer' | cut -d':' -f2)"
echo "GPU环境变量: $__GLX_VENDOR_LIBRARY_NAME"
echo ""

# 检查bag文件
BAG_FILE="/root/autodl-tmp/rtab_ws/src/turtlebot3_slam_3d/config/bags/complete_nolaser.bag"
if [ ! -f "$BAG_FILE" ]; then
    echo "❌ 错误: bag文件不存在: $BAG_FILE"
    echo "请检查文件路径"
    exit 1
fi

echo "✅ 找到bag文件: $BAG_FILE"
BAG_SIZE=$(du -h "$BAG_FILE" | cut -f1)
echo "文件大小: $BAG_SIZE"
echo ""

# 显示启动参数
echo "🎯 启动参数:"
echo "- bag文件: $BAG_FILE"
echo "- GPU加速: 已启用"
echo "- 外部里程计: false (纯视觉SLAM)"
echo "- 播放速率: 0.5x"
echo ""

echo "💡 性能监控建议:"
echo "在另一个终端运行以下命令监控性能:"
echo "1. GPU使用率: watch nvidia-smi"
echo "2. 系统资源: htop"
echo "3. ROS话题: rostopic hz /camera/color/image_raw"
echo ""

# 提示用户
echo "⚠️  注意事项:"
echo "- 确保X11转发正常工作"
echo "- RViz窗口可能需要几秒钟才能出现"
echo "- 如果RViz卡顿，检查GPU使用率"
echo ""

read -p "按Enter键开始启动，或Ctrl+C取消..."
echo ""

echo "🚀 启动GPU加速的TurtleBot3 SLAM 3D Demo..."
echo "正在启动roslaunch..."

# 启动roslaunch
roslaunch turtlebot3_slam_3d demo_bag.launch \
    bag_file:="$BAG_FILE" \
    use_external_odom:=false \
    rate:=0.5

echo ""
echo "✅ Demo已结束"

# 显示结果信息
echo ""
echo "📊 查看结果:"
echo "============="
echo "1. RTAB-Map数据库位置: ~/.ros/rtabmap.db"
echo "2. 启动数据库查看器: rtabmap-databaseViewer ~/.ros/rtabmap.db"
echo "3. 检查生成的地图质量"
echo ""

echo "🔍 故障排除:"
echo "============="
echo "如果遇到问题:"
echo "1. 检查GPU加速: glxinfo | grep renderer"
echo "2. 验证环境变量: echo \$__GLX_VENDOR_LIBRARY_NAME"
echo "3. 重新加载GPU环境: source gpu_env.sh"
echo "4. 检查RViz进程: ps aux | grep rviz"
