#ifndef VOXBLOX_ADAPTER_H
#define VOXBLOX_ADAPTER_H

#include <vector>
#include <unordered_map>
#include <memory>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <Eigen/Dense>
#include <visualization_msgs/MarkerArray.h>
#include <geometry_msgs/Point.h>
#include <std_msgs/ColorRGBA.h>

// 定义TSDFVoxel结构体
namespace tsdf_mapping {
    struct TSDFVoxel {
        float tsdf_value;
        float weight;
        uint8_t r, g, b;

        TSDFVoxel() : tsdf_value(0.0f), weight(0.0f), r(0), g(0), b(0) {}
    };
}

namespace tsdf_mapping {

/**
 * @brief 高效体素块结构 - 借鉴voxblox的分层设计思想
 */
struct VoxelBlock {
    static constexpr int kVoxelsPerSide = 16;  // 每个块包含16x16x16个体素
    static constexpr int kVoxelsPerBlock = kVoxelsPerSide * kVoxelsPerSide * kVoxelsPerSide;
    
    // 体素数据数组 - 直接内存访问，O(1)时间复杂度
    TSDFVoxel voxels[kVoxelsPerBlock];
    
    // 块级别的元数据
    bool has_data;
    int active_voxel_count;
    float min_tsdf, max_tsdf;
    
    VoxelBlock() : has_data(false), active_voxel_count(0), min_tsdf(1.0f), max_tsdf(-1.0f) {
        // 初始化所有体素
        for (int i = 0; i < kVoxelsPerBlock; ++i) {
            voxels[i].tsdf_value = 0.0f;
            voxels[i].weight = 0.0f;
            voxels[i].r = voxels[i].g = voxels[i].b = 0;
        }
    }
    
    // 获取体素的线性索引
    inline int getLinearIndex(int x, int y, int z) const {
        return z * kVoxelsPerSide * kVoxelsPerSide + y * kVoxelsPerSide + x;
    }
    
    // 从线性索引获取3D坐标
    inline void getCoordinates(int linear_index, int& x, int& y, int& z) const {
        z = linear_index / (kVoxelsPerSide * kVoxelsPerSide);
        int remainder = linear_index % (kVoxelsPerSide * kVoxelsPerSide);
        y = remainder / kVoxelsPerSide;
        x = remainder % kVoxelsPerSide;
    }
    
    // 更新体素
    void updateVoxel(int x, int y, int z, const TSDFVoxel& new_voxel);
    
    // 获取体素
    const TSDFVoxel* getVoxel(int x, int y, int z) const;
    TSDFVoxel* getVoxel(int x, int y, int z);
};

/**
 * @brief 块索引结构 - 用于哈希表的键
 */
struct BlockIndex {
    int x, y, z;

    BlockIndex() : x(0), y(0), z(0) {}  // 默认构造函数
    BlockIndex(int x_, int y_, int z_) : x(x_), y(y_), z(z_) {}

    bool operator==(const BlockIndex& other) const {
        return x == other.x && y == other.y && z == other.z;
    }
};

/**
 * @brief 块索引哈希函数
 */
struct BlockIndexHash {
    std::size_t operator()(const BlockIndex& idx) const {
        // 使用简单但高效的哈希函数
        return std::hash<int>()(idx.x) ^ 
               (std::hash<int>()(idx.y) << 1) ^ 
               (std::hash<int>()(idx.z) << 2);
    }
};

/**
 * @brief GPU体素更新数据结构
 */
struct LinearVoxelUpdate {
    int block_x, block_y, block_z;  // 块坐标
    int voxel_x, voxel_y, voxel_z;  // 块内体素坐标
    float tsdf_value;
    float weight;
    uint8_t r, g, b;
};

/**
 * @brief VoxBlox风格的高效TSDF体素存储适配器
 */
class VoxbloxAdapter {
public:
    VoxbloxAdapter(float voxel_size, float block_size);
    ~VoxbloxAdapter() = default;
    
    // 批量更新体素 - 主要接口
    void batchUpdateVoxels(const std::vector<LinearVoxelUpdate>& updates);
    
    // 单个体素更新
    void updateVoxel(float world_x, float world_y, float world_z, const TSDFVoxel& voxel);
    
    // 获取体素
    const TSDFVoxel* getVoxel(float world_x, float world_y, float world_z) const;
    
    // 生成点云
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr generatePointCloud(float min_weight_threshold = 0.1f) const;

    // 🆕 生成体素网格可视化
    visualization_msgs::MarkerArray generateVoxelMarkers(float min_weight_threshold = 0.01f) const;

    // 🆕 生成体素网格数据
    struct VoxelGridData {
        std::vector<geometry_msgs::Point> positions;
        std::vector<std_msgs::ColorRGBA> colors;
        std::vector<float> tsdf_values;
        std::vector<float> weights;
        float voxel_size;
        size_t total_voxels;
    };
    VoxelGridData generateVoxelGrid(float min_weight_threshold = 0.1f) const;
    
    // 获取统计信息
    struct Statistics {
        size_t total_blocks;
        size_t active_blocks;
        size_t total_voxels;
        size_t active_voxels;
        size_t memory_usage_bytes;
    };
    Statistics getStatistics() const;
    
    // 清理低权重体素
    void pruneVoxels(float min_weight_threshold = 0.01f);

    // 🔧 动态体素原点支持（配合双重变换机制）
    void updateVolumeOrigin(const Eigen::Vector3f& new_origin);
    Eigen::Vector3f getVolumeOrigin() const { return volume_origin_; }

private:
    // 配置参数
    float voxel_size_;
    float block_size_;
    int voxels_per_block_side_;
    
    // 体素块存储 - 核心数据结构
    std::unordered_map<BlockIndex, std::unique_ptr<VoxelBlock>, BlockIndexHash> blocks_;
    
    // 坐标转换函数
    BlockIndex worldToBlockIndex(float world_x, float world_y, float world_z) const;
    void worldToVoxelIndex(float world_x, float world_y, float world_z, 
                          BlockIndex& block_idx, int& voxel_x, int& voxel_y, int& voxel_z) const;
    void voxelToWorldCoordinates(const BlockIndex& block_idx, int voxel_x, int voxel_y, int voxel_z,
                                float& world_x, float& world_y, float& world_z) const;
    
    // 获取或创建体素块
    VoxelBlock* getOrCreateBlock(const BlockIndex& block_idx);
    const VoxelBlock* getBlock(const BlockIndex& block_idx) const;
    
    // 性能统计
    mutable size_t update_count_;
    mutable size_t lookup_count_;

    // 🔧 动态体素原点相关成员（支持双重变换机制）
    Eigen::Vector3f volume_origin_;  // 当前体素网格原点（世界坐标系）
    bool volume_origin_initialized_;  // 体素原点是否已初始化
};

} // namespace tsdf_mapping

#endif // VOXBLOX_ADAPTER_H
