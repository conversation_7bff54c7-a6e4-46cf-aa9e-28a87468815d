#ifndef TSDF_FUSION_H
#define TSDF_FUSION_H

#include <ros/ros.h>
#include <sensor_msgs/Image.h>
#include <sensor_msgs/CameraInfo.h>
#include <sensor_msgs/PointCloud2.h>
#include <visualization_msgs/MarkerArray.h>
#include <cv_bridge/cv_bridge.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <pcl_conversions/pcl_conversions.h>
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <geometry_msgs/TransformStamped.h>
#include <nav_msgs/Odometry.h>  // 用于RTAB-Map位姿订阅
#include <Eigen/Dense>
#include <unordered_map>
#include <memory>
#include <mutex>

// 🚀 GPU加速：包含CUDA头文件
#include "tsdf_mapping/tsdf_cuda.h"
#include "tsdf_mapping/voxblox_adapter.h"

namespace tsdf_mapping {

// TSDFVoxel现在在voxblox_adapter.h中定义

struct VoxelIndex {
    int x, y, z;

    VoxelIndex(int x = 0, int y = 0, int z = 0) : x(x), y(y), z(z) {}

    bool operator==(const VoxelIndex& other) const {
        return x == other.x && y == other.y && z == other.z;
    }
};

struct VoxelIndexHash {
    std::size_t operator()(const VoxelIndex& idx) const {
        return std::hash<int>()(idx.x) ^
               (std::hash<int>()(idx.y) << 1) ^
               (std::hash<int>()(idx.z) << 2);
    }
};

// 坐标系类型枚举
enum class CoordinateFrameType {
    OPTICAL,    // 光学坐标系 (X右, Y下, Z前)
    MECHANICAL, // 机械坐标系 (X前, Y左, Z上)
    UNKNOWN     // 未知坐标系
};

class TSDFFusion {
public:
    TSDFFusion(ros::NodeHandle& nh);
    ~TSDFFusion();

    bool initialize();
    void processRGBD(const sensor_msgs::ImageConstPtr& rgb_msg,
                     const sensor_msgs::ImageConstPtr& depth_msg,
                     const sensor_msgs::CameraInfoConstPtr& camera_info);
    void publishVisualization();
    void reset();

private:
    ros::NodeHandle nh_;
    ros::Publisher pointcloud_pub_;
    ros::Publisher mesh_pub_;
    ros::Publisher marker_pub_;
    ros::Publisher voxel_markers_pub_;  // 🆕 体素标记发布器
    ros::Publisher voxel_grid_pub_;     // 🆕 体素网格发布器
    ros::Timer voxel_visualization_timer_;  // 🔧 体素可视化定时器

    // RTAB-Map位姿融合相关
    ros::Subscriber rtabmap_odom_sub_;  // RTAB-Map位姿订阅器

    // TF相关
    std::unique_ptr<tf2_ros::Buffer> tf_buffer_;
    std::unique_ptr<tf2_ros::TransformListener> tf_listener_;

    // TSDF参数
    float voxel_size_;
    float truncation_distance_;
    float max_weight_;
    std::string world_frame_;
    std::string camera_frame_;

    // 相机参数
    Eigen::Matrix3f camera_intrinsics_;
    bool camera_info_received_;

    // TSDF体积 - 传统存储（保留作为备份）
    std::unordered_map<VoxelIndex, TSDFVoxel, VoxelIndexHash> tsdf_volume_;

    // 🚀 VoxBlox风格高效存储
    std::unique_ptr<VoxbloxAdapter> voxblox_adapter_;
    bool use_voxblox_storage_;  // 是否使用VoxBlox存储

    // 🚀 VoxBlox质量优化参数
    float voxblox_min_weight_threshold_;  // 最小权重阈值
    float voxblox_surface_threshold_;     // 表面阈值
    bool enable_surface_smoothing_;       // 启用表面平滑
    bool enable_density_enhancement_;     // 启用密度增强

    // 坐标系转换相关
    Eigen::Matrix3f optical_to_mechanical_rotation_;  // 光学到机械坐标系转换矩阵
    Eigen::Matrix3f mechanical_to_optical_rotation_;  // 机械到光学坐标系转换矩阵



    // 质量控制参数
    float depth_min_, depth_max_;  // 深度范围限制
    bool enable_bilateral_filter_;  // 启用双边滤波
    bool enable_statistical_filter_;  // 启用统计滤波
    int statistical_filter_neighbors_;  // 统计滤波邻居数
    float statistical_filter_std_ratio_;  // 统计滤波标准差比率

    // 智能坐标系诊断相关
    int coordinate_mismatch_count_;  // 坐标系不匹配计数器

    // RTAB-Map位姿融合相关
    bool use_rtabmap_pose_;  // 是否使用RTAB-Map位姿
    bool enable_rtabmap_collaboration_;  // 是否启用协作建图
    bool disable_internal_coordinate_transform_;  // 是否禁用内部坐标变换
    nav_msgs::Odometry::ConstPtr latest_rtabmap_odom_;  // 最新的RTAB-Map位姿
    ros::Time last_rtabmap_odom_time_;  // 最后收到位姿的时间
    std::mutex rtabmap_odom_mutex_;  // 位姿数据保护锁

    // TSDF到RTAB-Map坐标系对齐相关
    bool enable_tsdf_rtabmap_alignment_;  // 是否启用TSDF到RTAB-Map坐标系对齐
    Eigen::Matrix4f tsdf_to_rtabmap_alignment_;  // TSDF到RTAB-Map坐标系对齐变换矩阵

    // 深度点云变换相关（专门修正深度方向，不影响位姿）
    bool enable_depth_pointcloud_transform_;  // 是否启用深度点云变换
    Eigen::Matrix3f depth_pointcloud_transform_;  // 深度点云专用变换矩阵

    // 曲率分析和法向量计算相关
    mutable cv::Mat depth_gradient_x_;  // 深度梯度X方向
    mutable cv::Mat depth_gradient_y_;  // 深度梯度Y方向
    mutable cv::Mat depth_gradient_magnitude_;  // 深度梯度幅值

    // 🚀 GPU加速相关成员（保持算法级融合架构）
    std::unique_ptr<TSDFCuda> gpu_tsdf_;  // GPU加速TSDF处理器
    bool enable_gpu_acceleration_;  // 是否启用GPU加速
    bool gpu_initialized_;  // GPU是否已初始化

    // 🔧 动态体素原点跟踪相关成员（支持双重变换机制）
    Eigen::Vector3f volume_origin_;  // 当前体素网格原点（世界坐标系）
    bool volume_origin_initialized_;  // 体素原点是否已初始化
    float volume_update_threshold_;  // 体素原点更新阈值（米）
    float migration_distance_threshold_;  // GPU体素迁移触发阈值（体素单位）
    Eigen::Vector3f last_robot_position_;  // 上次机器人位置（用于跟踪移动）

    /**
     * @brief 动态配置回调 (暂时禁用)
     */
    // void configCallback(tsdf_mapping::TSDFMappingConfig& config, uint32_t level);

    /**
     * @brief 辅助方法
     */
    VoxelIndex worldToVoxel(const Eigen::Vector3f& world_point) const;
    Eigen::Vector3f voxelToWorld(const VoxelIndex& voxel_idx) const;
    void updateTSDFVoxel(const VoxelIndex& voxel_idx, float sdf_value, float weight, const cv::Vec3b& color);
    void updateTSDFVoxelsAroundPoint(const Eigen::Vector3f& surface_point,
                                   float depth,
                                   const cv::Vec3b& color,
                                   const Eigen::Matrix4f& camera_pose,
                                   const Eigen::Vector3f& surface_normal);

    pcl::PointCloud<pcl::PointXYZRGB>::Ptr generateMesh();
    void publishQualityMarkers(const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud);
    void publishVoxelVisualization();  // 🆕 发布体素可视化
    void voxelVisualizationTimerCallback(const ros::TimerEvent& event);  // 🔧 体素可视化定时器回调
    void addTestVoxelData();  // 🔧 添加测试体素数据
    void generateTestPointCloud();

    /**
     * @brief 坐标系检测和转换方法
     */
    CoordinateFrameType detectCoordinateFrameType(const std::string& frame_id) const;
    Eigen::Vector3f applyCoordinateTransform(const Eigen::Vector3f& point,
                                           CoordinateFrameType from_type,
                                           CoordinateFrameType to_type) const;
    void initializeCoordinateTransforms();
    void initializeTSDFToRTABMapAlignment();
    void initializeDepthPointcloudTransform();  // 初始化深度点云变换矩阵



    /**
     * @brief 智能坐标系诊断和验证方法
     */
    void diagnoseCoordinateAlignment(const std::string& depth_frame_id,
                                   const std::string& effective_camera_frame,
                                   CoordinateFrameType depth_frame_type,
                                   CoordinateFrameType effective_frame_type,
                                   const geometry_msgs::TransformStamped& transform);
    void validateCoordinateConfiguration();

    /**
     * @brief RTAB-Map位姿融合相关方法
     */
    void rtabmapOdomCallback(const nav_msgs::Odometry::ConstPtr& odom_msg);
    bool getRTABMapPose(const ros::Time& target_time, geometry_msgs::TransformStamped& transform);
    void initializeRTABMapCollaboration();

    /**
     * @brief 质量控制相关方法
     */
    void initializeQualityControl();
    cv::Mat applyBilateralFilter(const cv::Mat& depth_image) const;
    cv::Mat preprocessDepthImage(const cv::Mat& depth_image) const;
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr applyStatisticalFilter(
        const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud) const;
    bool isValidDepth(float depth) const;
    float calculateAdaptiveWeight(float sdf_value, float depth) const;
    float calculateAdaptiveSurfaceThreshold(const TSDFVoxel& voxel, const VoxelIndex& idx) const;

    /**
     * @brief 表面法向量和曲率分析相关方法
     */
    Eigen::Vector3f calculateSurfaceNormal(int u, int v, float depth,
                                         const sensor_msgs::CameraInfoConstPtr& camera_info) const;
    Eigen::Vector2f estimateDepthGradient(int u, int v) const;
    float analyzeSurfaceCurvature(const Eigen::Vector3f& surface_normal,
                                 const Eigen::Vector3f& world_point) const;
    int classifyCurvatureLevel(float curvature) const;

    /**
     * @brief 多尺度更新策略方法
     */
    void updateSingleScaleVoxels(const Eigen::Vector3f& surface_point, float depth,
                                const cv::Vec3b& color, const Eigen::Matrix4f& camera_pose,
                                const Eigen::Vector3f& surface_normal);
    void updateDualScaleVoxels(const Eigen::Vector3f& surface_point, float depth,
                              const cv::Vec3b& color, const Eigen::Matrix4f& camera_pose,
                              const Eigen::Vector3f& surface_normal);
    void updateTripleScaleVoxels(const Eigen::Vector3f& surface_point, float depth,
                                const cv::Vec3b& color, const Eigen::Matrix4f& camera_pose,
                                const Eigen::Vector3f& surface_normal);

    /**
     * @brief 拓扑连接构建相关方法
     */
    void buildTopologicalConnections();
    bool validateSurfaceContinuity(const VoxelIndex& voxel_idx) const;
    void repairSurfaceGap(const VoxelIndex& voxel_idx);
    void smoothSurfaceTransitions();

    /**
     * @brief 🚀 GPU加速相关方法（保持算法级融合架构）
     */
    bool initializeGPUAcceleration();
    bool processRGBDWithGPU(const sensor_msgs::ImageConstPtr& rgb_msg,
                            const sensor_msgs::ImageConstPtr& depth_msg,
                            const sensor_msgs::CameraInfoConstPtr& camera_info,
                            const Eigen::Matrix4f& camera_pose);
    void cleanupGPUResources();
    bool isGPUAvailable() const;
    void getGPUPerformanceStats() const;
    void updateDynamicVolumeOrigin(const Eigen::Matrix4f& camera_pose);

    /**
     * @brief 🔄 混合策略：从GPU传输体素数据到CPU
     */
    void transferGPUVoxelsToCPU();

    /**
     * @brief 🔧 动态体素原点管理相关方法（支持双重变换机制）
     */
    void initializeVolumeOrigin();
    void migrateVoxelData(const Eigen::Vector3f& old_origin, const Eigen::Vector3f& new_origin);

};

} // namespace tsdf_mapping

#endif // TSDF_FUSION_H
