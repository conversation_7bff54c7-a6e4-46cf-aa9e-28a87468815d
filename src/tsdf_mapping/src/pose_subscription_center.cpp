/**
 * 位姿订阅中心节点 - 实现流程图设计的订阅位姿式融合
 * 
 * 功能：
 * 1. 订阅RTAB-Map的TF变换 (map->base_link)
 * 2. 转换为标准化的Odometry话题
 * 3. 确保时间戳与传感器数据同步
 * 4. 发布到/pose_center/odom话题供TSDF订阅
 * 
 * 作者：AI Assistant
 * 日期：2025-07-23
 */

#include <ros/ros.h>
#include <nav_msgs/Odometry.h>
#include <geometry_msgs/TransformStamped.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_ros/buffer.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <tf2/LinearMath/Transform.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <mutex>
#include <Eigen/Dense>
#include <Eigen/Geometry>

class PoseSubscriptionCenter
{
public:
    PoseSubscriptionCenter() : 
        tf_buffer_(ros::Duration(10.0)),
        tf_listener_(tf_buffer_),
        last_pose_time_(0.0),
        pose_quality_threshold_(0.1)
    {
        ros::NodeHandle nh;
        ros::NodeHandle pnh("~");
        
        // 参数配置
        pnh.param<std::string>("source_frame", source_frame_, "map");
        pnh.param<std::string>("target_frame", target_frame_, "base_link");
        pnh.param<double>("publish_rate", publish_rate_, 30.0);
        pnh.param<double>("pose_quality_threshold", pose_quality_threshold_, 0.1);
        pnh.param<bool>("enable_pose_filtering", enable_pose_filtering_, true);
        pnh.param<bool>("use_odom_backup", use_odom_backup_, true);

        // 坐标系校正参数
        pnh.param<bool>("enable_coordinate_correction", enable_coordinate_correction_, true);
        pnh.param<std::string>("camera_frame", camera_frame_, "zed_left_camera_optical_frame");
        pnh.param<bool>("apply_optical_to_mechanical_transform", apply_optical_to_mechanical_transform_, true);

        // 发布器
        odom_pub_ = nh.advertise<nav_msgs::Odometry>("/pose_center/odom", 10);

        // 多源位姿订阅 - TF + odom话题双重备份
        if (use_odom_backup_) {
            rtabmap_odom_sub_ = nh.subscribe("/rtabmap/odom", 10,
                                           &PoseSubscriptionCenter::rtabmapOdomCallback, this);
            ROS_INFO("✅ 启用odom话题备份订阅: /rtabmap/odom");
        }
        
        // 定时器
        timer_ = nh.createTimer(ros::Duration(1.0 / publish_rate_), 
                               &PoseSubscriptionCenter::timerCallback, this);
        
        // 初始化坐标系校正
        initializeCoordinateCorrection();

        ROS_INFO("🎯 位姿订阅中心启动");
        ROS_INFO("   源坐标系: %s", source_frame_.c_str());
        ROS_INFO("   目标坐标系: %s", target_frame_.c_str());
        ROS_INFO("   相机坐标系: %s", camera_frame_.c_str());
        ROS_INFO("   发布频率: %.1f Hz", publish_rate_);
        ROS_INFO("   坐标系校正: %s", enable_coordinate_correction_ ? "启用" : "禁用");
        ROS_INFO("   光学->机械变换: %s", apply_optical_to_mechanical_transform_ ? "启用" : "禁用");
    }

private:
    void timerCallback(const ros::TimerEvent& event)
    {
        nav_msgs::Odometry odom_msg;
        bool pose_obtained = false;

        // 🎯 算法级融合修复：直接使用RTAB-Map位姿，保持坐标系一致性
        if (use_odom_backup_ && latest_rtabmap_odom_) {
            ROS_INFO_THROTTLE(2.0, "🎯 算法级融合：直接使用RTAB-Map位姿数据，保持原始坐标系");

            odom_msg = *latest_rtabmap_odom_;
            odom_msg.header.frame_id = source_frame_;  // 保持map坐标系
            odom_msg.child_frame_id = target_frame_;   // 保持base_link

            // 更新时间戳为当前仿真时间
            odom_msg.header.stamp = ros::Time::now();

            pose_obtained = true;
            pose_source_ = "rtabmap_odom";  // 标记数据源为RTAB-Map
            ROS_INFO_THROTTLE(2.0, "✅ 算法级融合：RTAB-Map位姿 [%.3f, %.3f, %.3f] (无额外变换)",
                             odom_msg.pose.pose.position.x,
                             odom_msg.pose.pose.position.y,
                             odom_msg.pose.pose.position.z);
        }

        // 🔧 强化TF回退机制：如果odom不可用，强制使用TF查找
        if (!pose_obtained) {
            geometry_msgs::TransformStamped transform;
            try {
                // 🔧 强力修复：使用最新可用时间进行TF查找
                transform = tf_buffer_.lookupTransform(source_frame_, target_frame_,
                                                      ros::Time(0), ros::Duration(2.0));  // 增加超时时间
                transformToOdometry(transform, odom_msg);
                pose_obtained = true;
                pose_source_ = "tf";
                ROS_INFO_THROTTLE(2.0, "✅ 强化TF回退：使用TF变换位姿 [%.3f, %.3f, %.3f]",
                                 odom_msg.pose.pose.position.x,
                                 odom_msg.pose.pose.position.y,
                                 odom_msg.pose.pose.position.z);
            } catch (tf2::TransformException& ex) {
                ROS_WARN_THROTTLE(2.0, "❌ TF查找失败: %s", ex.what());
                return;
            }
        }

        // 位姿质量检查
        if (enable_pose_filtering_ && !isPoseQualityGood(odom_msg)) {
            ROS_WARN_THROTTLE(5.0, "⚠️ 位姿质量不佳，跳过发布");
            return;
        }

        // 🎯 算法级融合关键修复：根据数据源决定是否进行坐标变换
        if (enable_coordinate_correction_ && pose_source_ != "rtabmap_odom") {
            // 只对非RTAB-Map数据源进行坐标变换
            ROS_INFO_THROTTLE(5.0, "🔄 对%s数据源应用坐标变换", pose_source_.c_str());

            geometry_msgs::Transform corrected_transform;
            corrected_transform.translation.x = odom_msg.pose.pose.position.x;
            corrected_transform.translation.y = odom_msg.pose.pose.position.y;
            corrected_transform.translation.z = odom_msg.pose.pose.position.z;
            corrected_transform.rotation = odom_msg.pose.pose.orientation;

            corrected_transform = applyCorrectedTransform(corrected_transform);

            odom_msg.pose.pose.position.x = corrected_transform.translation.x;
            odom_msg.pose.pose.position.y = corrected_transform.translation.y;
            odom_msg.pose.pose.position.z = corrected_transform.translation.z;
            odom_msg.pose.pose.orientation = corrected_transform.rotation;
        } else if (pose_source_ == "rtabmap_odom") {
            // RTAB-Map数据源：保持原始坐标系，不进行额外变换
            ROS_INFO_THROTTLE(5.0, "🎯 RTAB-Map数据源：保持原始坐标系，跳过坐标变换");
        }

        // 发布统一的位姿数据
        odom_pub_.publish(odom_msg);

        // 更新统计信息
        last_pose_time_ = odom_msg.header.stamp.toSec();
        pose_count_++;

        // 增强调试输出：验证坐标变换正确性
        ROS_INFO_THROTTLE(3.0, "✅ 位姿订阅中心：发布位姿 #%d (源: %s)",
                         pose_count_, pose_source_.c_str());

        // 🔍 关键调试：监控位姿数据变化
        static geometry_msgs::Point last_position;
        static bool first_pose_published = true;

        if (first_pose_published) {
            last_position = odom_msg.pose.pose.position;
            first_pose_published = false;
            ROS_INFO("🎯 位姿订阅中心：首次位姿记录 [%.3f, %.3f, %.3f]",
                     last_position.x, last_position.y, last_position.z);
        } else {
            float dx = odom_msg.pose.pose.position.x - last_position.x;
            float dy = odom_msg.pose.pose.position.y - last_position.y;
            float dz = odom_msg.pose.pose.position.z - last_position.z;
            float movement = sqrt(dx*dx + dy*dy + dz*dz);

            ROS_INFO_THROTTLE(2.0, "🔍 位姿订阅中心位姿变化: 移动距离=%.4f米 [%.3f, %.3f, %.3f]",
                             movement, odom_msg.pose.pose.position.x,
                             odom_msg.pose.pose.position.y, odom_msg.pose.pose.position.z);

            if (movement > 0.001f) {  // 1mm以上的移动
                ROS_INFO("✅ 位姿订阅中心检测到移动: %.4f米", movement);
                last_position = odom_msg.pose.pose.position;
            }
        }
        ROS_INFO_THROTTLE(3.0, "🎯 位姿订阅中心输出 (%s): 位置[%.3f, %.3f, %.3f] 旋转[%.3f, %.3f, %.3f, %.3f]",
                          pose_source_.c_str(),
                          odom_msg.pose.pose.position.x, odom_msg.pose.pose.position.y, odom_msg.pose.pose.position.z,
                          odom_msg.pose.pose.orientation.x, odom_msg.pose.pose.orientation.y,
                          odom_msg.pose.pose.orientation.z, odom_msg.pose.pose.orientation.w);
    }
    
    void transformToOdometry(const geometry_msgs::TransformStamped& transform,
                           nav_msgs::Odometry& odom)
    {
        odom.header = transform.header;
        odom.header.frame_id = source_frame_;
        odom.child_frame_id = target_frame_;

        // 🎯 条件性坐标系校正：只对TF数据源进行变换
        geometry_msgs::Transform corrected_transform = transform.transform;
        if (enable_coordinate_correction_ && pose_source_ == "tf") {
            // 只对TF数据源进行坐标变换，RTAB-Map数据源保持原样
            corrected_transform = applyCorrectedTransform(transform.transform);
            ROS_INFO_THROTTLE(5.0, "🔄 TF数据源：应用坐标变换");
        } else {
            ROS_INFO_THROTTLE(5.0, "🎯 保持原始坐标系 (数据源: %s)", pose_source_.c_str());
        }

        // 位置
        odom.pose.pose.position.x = corrected_transform.translation.x;
        odom.pose.pose.position.y = corrected_transform.translation.y;
        odom.pose.pose.position.z = corrected_transform.translation.z;

        // 旋转
        odom.pose.pose.orientation = corrected_transform.rotation;
        
        // 协方差矩阵（简化版本）
        for (int i = 0; i < 36; i++) {
            odom.pose.covariance[i] = 0.0;
            odom.twist.covariance[i] = 0.0;
        }
        
        // 设置位置协方差
        odom.pose.covariance[0] = 0.01;  // x
        odom.pose.covariance[7] = 0.01;  // y
        odom.pose.covariance[14] = 0.01; // z
        odom.pose.covariance[21] = 0.01; // roll
        odom.pose.covariance[28] = 0.01; // pitch
        odom.pose.covariance[35] = 0.01; // yaw
        
        // 速度信息（如果需要可以计算）
        odom.twist.twist.linear.x = 0.0;
        odom.twist.twist.linear.y = 0.0;
        odom.twist.twist.linear.z = 0.0;
        odom.twist.twist.angular.x = 0.0;
        odom.twist.twist.angular.y = 0.0;
        odom.twist.twist.angular.z = 0.0;
    }
    
    bool isPoseQualityGood(const geometry_msgs::TransformStamped& transform)
    {
        // 检查时间戳是否合理
        double current_time = ros::Time::now().toSec();
        double pose_time = transform.header.stamp.toSec();
        
        if (std::abs(current_time - pose_time) > 1.0) {
            ROS_WARN_THROTTLE(5.0, "位姿时间戳过旧: %.3f秒", current_time - pose_time);
            return false;
        }
        
        // 检查位置是否合理（避免异常值）
        double pos_magnitude = std::sqrt(
            transform.transform.translation.x * transform.transform.translation.x +
            transform.transform.translation.y * transform.transform.translation.y +
            transform.transform.translation.z * transform.transform.translation.z
        );
        
        if (pos_magnitude > 100.0) {  // 100米阈值
            ROS_WARN_THROTTLE(5.0, "位置异常: 距离原点%.2f米", pos_magnitude);
            return false;
        }
        
        // 检查旋转四元数是否归一化
        double quat_norm = std::sqrt(
            transform.transform.rotation.x * transform.transform.rotation.x +
            transform.transform.rotation.y * transform.transform.rotation.y +
            transform.transform.rotation.z * transform.transform.rotation.z +
            transform.transform.rotation.w * transform.transform.rotation.w
        );
        
        if (std::abs(quat_norm - 1.0) > 0.1) {
            ROS_WARN_THROTTLE(5.0, "四元数未归一化: norm=%.3f", quat_norm);
            return false;
        }
        
        return true;
    }

    void rtabmapOdomCallback(const nav_msgs::Odometry::ConstPtr& odom_msg)
    {
        std::lock_guard<std::mutex> lock(odom_mutex_);
        latest_rtabmap_odom_ = odom_msg;

        // 🔍 关键调试：监控RTAB-Map odom数据接收
        static int odom_count = 0;
        odom_count++;

        ROS_INFO_THROTTLE(3.0, "🔍 收到RTAB-Map odom数据 #%d，时间戳: %.3f，位置[%.3f, %.3f, %.3f]",
                          odom_count, odom_msg->header.stamp.toSec(),
                          odom_msg->pose.pose.position.x, odom_msg->pose.pose.position.y, odom_msg->pose.pose.position.z);
    }

    void initializeCoordinateCorrection()
    {
        // 🎯 修复：统一坐标系基准 - 确保与RTAB-Map完全一致
        // 光学坐标系定义: X右, Y下, Z前 (右手坐标系)
        // 机械坐标系定义: X前, Y左, Z上 (ROS标准右手坐标系)
        // RTAB-Map坐标系: X前, Y左, Z上 (标准ROS机械坐标系)

        // 🔧 修正后的变换矩阵 - 直接输出RTAB-Map兼容的坐标系
        // 目标：确保位姿订阅中心的输出与RTAB-Map使用完全相同的坐标系基准
        //
        // 标准光学到机械坐标系变换（与RTAB-Map一致）:
        // X_mechanical = Z_optical (前方向)
        // Y_mechanical = -X_optical (左方向)
        // Z_mechanical = -Y_optical (上方向)
        optical_to_mechanical_rotation_ << 0, 0, 1,    // [0 0 1] * [X Y Z]_optical = Z_optical = X_mechanical
                                          -1, 0, 0,    // [-1 0 0] * [X Y Z]_optical = -X_optical = Y_mechanical
                                           0,-1, 0;    // [0 -1 0] * [X Y Z]_optical = -Y_optical = Z_mechanical

        // 机械坐标系到光学坐标系的逆变换
        mechanical_to_optical_rotation_ = optical_to_mechanical_rotation_.transpose();

        // 查找base_link到camera_frame的静态变换
        try {
            base_to_camera_transform_ = tf_buffer_.lookupTransform(target_frame_, camera_frame_,
                                                                  ros::Time(0), ros::Duration(5.0));
            has_base_to_camera_transform_ = true;
            ROS_INFO("✅ 成功获取base_link到camera_frame变换: [%.3f, %.3f, %.3f]",
                     base_to_camera_transform_.transform.translation.x,
                     base_to_camera_transform_.transform.translation.y,
                     base_to_camera_transform_.transform.translation.z);
        } catch (tf2::TransformException& ex) {
            ROS_WARN("⚠️ 无法获取base_link到camera_frame变换: %s", ex.what());
            ROS_WARN("   将使用默认变换或跳过camera_frame校正");
            has_base_to_camera_transform_ = false;
        }

        // 🎯 输出修正后的变换矩阵以验证正确性
        ROS_INFO("🎯 位姿订阅中心坐标变换矩阵 (光学→机械，与RTAB-Map一致):");
        ROS_INFO("  [%6.2f %6.2f %6.2f]",
                 optical_to_mechanical_rotation_(0,0), optical_to_mechanical_rotation_(0,1), optical_to_mechanical_rotation_(0,2));
        ROS_INFO("  [%6.2f %6.2f %6.2f]",
                 optical_to_mechanical_rotation_(1,0), optical_to_mechanical_rotation_(1,1), optical_to_mechanical_rotation_(1,2));
        ROS_INFO("  [%6.2f %6.2f %6.2f]",
                 optical_to_mechanical_rotation_(2,0), optical_to_mechanical_rotation_(2,1), optical_to_mechanical_rotation_(2,2));
        ROS_INFO("  变换效果: X_mech=Z_opt, Y_mech=-X_opt, Z_mech=-Y_opt");
        ROS_INFO("✅ 坐标系校正初始化完成 (RTAB-Map兼容模式)");
    }

    geometry_msgs::Transform applyCorrectedTransform(const geometry_msgs::Transform& input_transform)
    {
        geometry_msgs::Transform corrected_transform = input_transform;

        if (!enable_coordinate_correction_) {
            ROS_DEBUG_THROTTLE(30.0, "🔄 坐标系校正已禁用，直接返回原始变换");
            return corrected_transform;
        }

        // 统一坐标变换策略：位姿订阅中心负责所有坐标系变换
        // 确保TSDF和RTAB-Map使用完全一致的坐标系基准

        // 步骤1: 应用base_link到camera_frame的变换（如果可用）
        if (has_base_to_camera_transform_) {
            // 将map->base_link变换扩展为map->camera_frame变换
            tf2::Transform map_to_base;
            tf2::fromMsg(input_transform, map_to_base);

            tf2::Transform base_to_camera;
            tf2::fromMsg(base_to_camera_transform_.transform, base_to_camera);

            tf2::Transform map_to_camera = map_to_base * base_to_camera;
            corrected_transform = tf2::toMsg(map_to_camera);

            ROS_DEBUG_THROTTLE(10.0, "✅ 位姿订阅中心：应用base_link到camera_frame变换");
        }

        // 步骤2: 应用光学坐标系到机械坐标系的变换（如果启用）
        if (apply_optical_to_mechanical_transform_) {
            // 转换位置
            Eigen::Vector3f position(corrected_transform.translation.x,
                                   corrected_transform.translation.y,
                                   corrected_transform.translation.z);

            Eigen::Vector3f corrected_position = optical_to_mechanical_rotation_ * position;

            corrected_transform.translation.x = corrected_position.x();
            corrected_transform.translation.y = corrected_position.y();
            corrected_transform.translation.z = corrected_position.z();

            // 转换旋转
            tf2::Quaternion original_quat;
            tf2::fromMsg(corrected_transform.rotation, original_quat);

            tf2::Matrix3x3 original_rotation(original_quat);

            // 将tf2::Matrix3x3转换为Eigen::Matrix3f
            Eigen::Matrix3f eigen_rotation;
            for (int i = 0; i < 3; i++) {
                for (int j = 0; j < 3; j++) {
                    eigen_rotation(i, j) = original_rotation[i][j];
                }
            }

            // 应用坐标系变换
            Eigen::Matrix3f corrected_rotation = optical_to_mechanical_rotation_ * eigen_rotation;

            // 转换回tf2::Matrix3x3
            tf2::Matrix3x3 corrected_tf2_rotation;
            for (int i = 0; i < 3; i++) {
                for (int j = 0; j < 3; j++) {
                    corrected_tf2_rotation[i][j] = corrected_rotation(i, j);
                }
            }

            tf2::Quaternion corrected_quat;
            corrected_tf2_rotation.getRotation(corrected_quat);
            corrected_transform.rotation = tf2::toMsg(corrected_quat);

            ROS_DEBUG_THROTTLE(10.0, "✅ 位姿订阅中心：应用光学到机械坐标系变换");
        }

        // 🎯 增强调试输出：详细显示坐标变换过程
        ROS_INFO_THROTTLE(10.0, "🎯 位姿订阅中心坐标变换完成:");
        ROS_INFO_THROTTLE(10.0, "   输入位置: [%.3f, %.3f, %.3f]",
                          input_transform.translation.x, input_transform.translation.y, input_transform.translation.z);
        ROS_INFO_THROTTLE(10.0, "   输出位置: [%.3f, %.3f, %.3f]",
                          corrected_transform.translation.x, corrected_transform.translation.y, corrected_transform.translation.z);
        ROS_INFO_THROTTLE(10.0, "   变换效果: 光学坐标系 -> RTAB-Map兼容机械坐标系");

        return corrected_transform;
    }

    bool isPoseQualityGood(const nav_msgs::Odometry& odom)
    {
        // 检查时间戳是否合理
        double current_time = ros::Time::now().toSec();
        double pose_time = odom.header.stamp.toSec();

        if (std::abs(current_time - pose_time) > 2.0) {
            ROS_WARN_THROTTLE(5.0, "位姿时间戳过旧: %.3f秒", current_time - pose_time);
            return false;
        }

        // 检查位置是否合理
        double pos_magnitude = std::sqrt(
            odom.pose.pose.position.x * odom.pose.pose.position.x +
            odom.pose.pose.position.y * odom.pose.pose.position.y +
            odom.pose.pose.position.z * odom.pose.pose.position.z
        );

        if (pos_magnitude > 100.0) {
            ROS_WARN_THROTTLE(5.0, "位置异常: 距离原点%.2f米", pos_magnitude);
            return false;
        }

        return true;
    }

private:
    // TF相关
    tf2_ros::Buffer tf_buffer_;
    tf2_ros::TransformListener tf_listener_;
    
    // ROS相关
    ros::Publisher odom_pub_;
    ros::Subscriber rtabmap_odom_sub_;
    ros::Timer timer_;

    // 参数
    std::string source_frame_;
    std::string target_frame_;
    std::string camera_frame_;
    double publish_rate_;
    double pose_quality_threshold_;
    bool enable_pose_filtering_;
    bool use_odom_backup_;

    // 坐标系校正参数
    bool enable_coordinate_correction_;
    bool apply_optical_to_mechanical_transform_;
    bool has_base_to_camera_transform_;

    // 坐标系变换矩阵
    Eigen::Matrix3f optical_to_mechanical_rotation_;
    Eigen::Matrix3f mechanical_to_optical_rotation_;
    geometry_msgs::TransformStamped base_to_camera_transform_;

    // 多源位姿数据
    nav_msgs::Odometry::ConstPtr latest_rtabmap_odom_;
    std::mutex odom_mutex_;
    std::string pose_source_;

    // 状态
    double last_pose_time_;
    int pose_count_ = 0;
};

int main(int argc, char** argv)
{
    ros::init(argc, argv, "pose_subscription_center");
    
    try {
        PoseSubscriptionCenter center;
        
        ROS_INFO("🚀 位姿订阅中心节点启动完成");
        ros::spin();
        
    } catch (const std::exception& e) {
        ROS_ERROR("位姿订阅中心启动失败: %s", e.what());
        return -1;
    }
    
    return 0;
}
