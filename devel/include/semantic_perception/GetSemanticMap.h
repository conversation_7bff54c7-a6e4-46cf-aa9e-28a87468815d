// Generated by gencpp from file semantic_perception/GetSemanticMap.msg
// DO NOT EDIT!


#ifndef SEMANTIC_PERCEPTION_MESSAGE_GETSEMANTICMAP_H
#define SEMANTIC_PERCEPTION_MESSAGE_GETSEMANTICMAP_H

#include <ros/service_traits.h>


#include <semantic_perception/GetSemanticMapRequest.h>
#include <semantic_perception/GetSemanticMapResponse.h>


namespace semantic_perception
{

struct GetSemanticMap
{

typedef GetSemanticMapRequest Request;
typedef GetSemanticMapResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetSemanticMap
} // namespace semantic_perception


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::semantic_perception::GetSemanticMap > {
  static const char* value()
  {
    return "1b3b4dc6ceae8633f93e12bfea253fb5";
  }

  static const char* value(const ::semantic_perception::GetSemanticMap&) { return value(); }
};

template<>
struct DataType< ::semantic_perception::GetSemanticMap > {
  static const char* value()
  {
    return "semantic_perception/GetSemanticMap";
  }

  static const char* value(const ::semantic_perception::GetSemanticMap&) { return value(); }
};


// service_traits::MD5Sum< ::semantic_perception::GetSemanticMapRequest> should match
// service_traits::MD5Sum< ::semantic_perception::GetSemanticMap >
template<>
struct MD5Sum< ::semantic_perception::GetSemanticMapRequest>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_perception::GetSemanticMap >::value();
  }
  static const char* value(const ::semantic_perception::GetSemanticMapRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_perception::GetSemanticMapRequest> should match
// service_traits::DataType< ::semantic_perception::GetSemanticMap >
template<>
struct DataType< ::semantic_perception::GetSemanticMapRequest>
{
  static const char* value()
  {
    return DataType< ::semantic_perception::GetSemanticMap >::value();
  }
  static const char* value(const ::semantic_perception::GetSemanticMapRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::semantic_perception::GetSemanticMapResponse> should match
// service_traits::MD5Sum< ::semantic_perception::GetSemanticMap >
template<>
struct MD5Sum< ::semantic_perception::GetSemanticMapResponse>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_perception::GetSemanticMap >::value();
  }
  static const char* value(const ::semantic_perception::GetSemanticMapResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_perception::GetSemanticMapResponse> should match
// service_traits::DataType< ::semantic_perception::GetSemanticMap >
template<>
struct DataType< ::semantic_perception::GetSemanticMapResponse>
{
  static const char* value()
  {
    return DataType< ::semantic_perception::GetSemanticMap >::value();
  }
  static const char* value(const ::semantic_perception::GetSemanticMapResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // SEMANTIC_PERCEPTION_MESSAGE_GETSEMANTICMAP_H
