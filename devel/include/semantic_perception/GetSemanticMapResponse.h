// Generated by gencpp from file semantic_perception/GetSemanticMapResponse.msg
// DO NOT EDIT!


#ifndef SEMANTIC_PERCEPTION_MESSAGE_GETSEMANTICMAPRESPONSE_H
#define SEMANTIC_PERCEPTION_MESSAGE_GETSEMANTICMAPRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <semantic_perception/SemanticObject.h>

namespace semantic_perception
{
template <class ContainerAllocator>
struct GetSemanticMapResponse_
{
  typedef GetSemanticMapResponse_<ContainerAllocator> Type;

  GetSemanticMapResponse_()
    : success(false)
    , message()
    , objects()
    , total_objects(0)  {
    }
  GetSemanticMapResponse_(const ContainerAllocator& _alloc)
    : success(false)
    , message(_alloc)
    , objects(_alloc)
    , total_objects(0)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _message_type;
  _message_type message;

   typedef std::vector< ::semantic_perception::SemanticObject_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::semantic_perception::SemanticObject_<ContainerAllocator> >> _objects_type;
  _objects_type objects;

   typedef int32_t _total_objects_type;
  _total_objects_type total_objects;





  typedef boost::shared_ptr< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> const> ConstPtr;

}; // struct GetSemanticMapResponse_

typedef ::semantic_perception::GetSemanticMapResponse_<std::allocator<void> > GetSemanticMapResponse;

typedef boost::shared_ptr< ::semantic_perception::GetSemanticMapResponse > GetSemanticMapResponsePtr;
typedef boost::shared_ptr< ::semantic_perception::GetSemanticMapResponse const> GetSemanticMapResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator1> & lhs, const ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success &&
    lhs.message == rhs.message &&
    lhs.objects == rhs.objects &&
    lhs.total_objects == rhs.total_objects;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator1> & lhs, const ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_perception

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "ae6e7db2d7936c87cba3491584c7a29d";
  }

  static const char* value(const ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xae6e7db2d7936c87ULL;
  static const uint64_t static_value2 = 0xcba3491584c7a29dULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_perception/GetSemanticMapResponse";
  }

  static const char* value(const ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"# 响应\n"
"bool success                    # 是否成功\n"
"string message                  # 状态消息\n"
"SemanticObject[] objects        # 检测到的语义物体列表\n"
"int32 total_objects            # 总物体数量\n"
"\n"
"\n"
"================================================================================\n"
"MSG: semantic_perception/SemanticObject\n"
"# 语义物体消息\n"
"Header header\n"
"\n"
"# 物体基本信息\n"
"string object_id                     # 物体唯一标识符\n"
"string class_name                    # 物体类别名称\n"
"int32 class_id                       # 物体类别ID\n"
"float32 confidence                   # 检测置信度 [0.0, 1.0]\n"
"\n"
"# 2D信息\n"
"float32 bbox_center_x                # 2D边界框中心X (像素坐标)\n"
"float32 bbox_center_y                # 2D边界框中心Y (像素坐标)\n"
"float32 bbox_width                   # 边界框宽度 (像素)\n"
"float32 bbox_height                  # 边界框高度 (像素)\n"
"sensor_msgs/Image mask               # 分割掩码 (可选)\n"
"\n"
"# 3D信息\n"
"bool has_3d_info                     # 是否有3D信息\n"
"geometry_msgs/Point position         # 3D位置 (世界坐标系)\n"
"geometry_msgs/Vector3 size           # 3D尺寸\n"
"geometry_msgs/Quaternion orientation # 3D方向\n"
"\n"
"# 语义属性\n"
"bool is_static                       # 是否为静态物体\n"
"bool is_navigable                    # 是否可通行\n"
"bool is_obstacle                     # 是否为障碍物\n"
"bool is_fire                         # 是否为火焰\n"
"bool is_dangerous                    # 是否危险\n"
"\n"
"# 追踪信息\n"
"int32 track_id                       # 追踪ID (-1表示未追踪)\n"
"int32 age                           # 物体年龄 (帧数)\n"
"float32 velocity                     # 运动速度 (m/s)\n"
"\n"
"# 时间戳\n"
"uint32 first_seen                   # 首次检测时间\n"
"uint32 last_seen                    # 最后检测时间\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: sensor_msgs/Image\n"
"# This message contains an uncompressed image\n"
"# (0, 0) is at top-left corner of image\n"
"#\n"
"\n"
"Header header        # Header timestamp should be acquisition time of image\n"
"                     # Header frame_id should be optical frame of camera\n"
"                     # origin of frame should be optical center of camera\n"
"                     # +x should point to the right in the image\n"
"                     # +y should point down in the image\n"
"                     # +z should point into to plane of the image\n"
"                     # If the frame_id here and the frame_id of the CameraInfo\n"
"                     # message associated with the image conflict\n"
"                     # the behavior is undefined\n"
"\n"
"uint32 height         # image height, that is, number of rows\n"
"uint32 width          # image width, that is, number of columns\n"
"\n"
"# The legal values for encoding are in file src/image_encodings.cpp\n"
"# If you want to standardize a new string format, join\n"
"# <EMAIL> and send an email proposing a new encoding.\n"
"\n"
"string encoding       # Encoding of pixels -- channel meaning, ordering, size\n"
"                      # taken from the list of strings in include/sensor_msgs/image_encodings.h\n"
"\n"
"uint8 is_bigendian    # is this data bigendian?\n"
"uint32 step           # Full row length in bytes\n"
"uint8[] data          # actual matrix data, size is (step * rows)\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
      stream.next(m.message);
      stream.next(m.objects);
      stream.next(m.total_objects);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetSemanticMapResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_perception::GetSemanticMapResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "message: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.message);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "objects: ";
    if (v.objects.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.objects.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::semantic_perception::SemanticObject_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.objects[i]);
    }
    if (v.objects.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "total_objects: ";
    Printer<int32_t>::stream(s, indent + "  ", v.total_objects);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_PERCEPTION_MESSAGE_GETSEMANTICMAPRESPONSE_H
