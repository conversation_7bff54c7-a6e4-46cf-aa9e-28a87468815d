// Generated by gencpp from file semantic_perception/SemanticSegmentation.msg
// DO NOT EDIT!


#ifndef SEMANTIC_PERCEPTION_MESSAGE_SEMANTICSEGMENTATION_H
#define SEMANTIC_PERCEPTION_MESSAGE_SEMANTICSEGMENTATION_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <sensor_msgs/Image.h>
#include <sensor_msgs/Image.h>
#include <sensor_msgs/Image.h>
#include <sensor_msgs/Image.h>
#include <sensor_msgs/CameraInfo.h>

namespace semantic_perception
{
template <class ContainerAllocator>
struct SemanticSegmentation_
{
  typedef SemanticSegmentation_<ContainerAllocator> Type;

  SemanticSegmentation_()
    : header()
    , image()
    , depth_image()
    , segmentation_mask()
    , confidence_map()
    , class_names()
    , class_ids()
    , class_colors()
    , num_classes(0)
    , mean_confidence(0.0)
    , processing_time(0.0)
    , camera_info()  {
    }
  SemanticSegmentation_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , image(_alloc)
    , depth_image(_alloc)
    , segmentation_mask(_alloc)
    , confidence_map(_alloc)
    , class_names(_alloc)
    , class_ids(_alloc)
    , class_colors(_alloc)
    , num_classes(0)
    , mean_confidence(0.0)
    , processing_time(0.0)
    , camera_info(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef  ::sensor_msgs::Image_<ContainerAllocator>  _image_type;
  _image_type image;

   typedef  ::sensor_msgs::Image_<ContainerAllocator>  _depth_image_type;
  _depth_image_type depth_image;

   typedef  ::sensor_msgs::Image_<ContainerAllocator>  _segmentation_mask_type;
  _segmentation_mask_type segmentation_mask;

   typedef  ::sensor_msgs::Image_<ContainerAllocator>  _confidence_map_type;
  _confidence_map_type confidence_map;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _class_names_type;
  _class_names_type class_names;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _class_ids_type;
  _class_ids_type class_ids;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _class_colors_type;
  _class_colors_type class_colors;

   typedef int32_t _num_classes_type;
  _num_classes_type num_classes;

   typedef float _mean_confidence_type;
  _mean_confidence_type mean_confidence;

   typedef float _processing_time_type;
  _processing_time_type processing_time;

   typedef  ::sensor_msgs::CameraInfo_<ContainerAllocator>  _camera_info_type;
  _camera_info_type camera_info;





  typedef boost::shared_ptr< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> const> ConstPtr;

}; // struct SemanticSegmentation_

typedef ::semantic_perception::SemanticSegmentation_<std::allocator<void> > SemanticSegmentation;

typedef boost::shared_ptr< ::semantic_perception::SemanticSegmentation > SemanticSegmentationPtr;
typedef boost::shared_ptr< ::semantic_perception::SemanticSegmentation const> SemanticSegmentationConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_perception::SemanticSegmentation_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_perception::SemanticSegmentation_<ContainerAllocator1> & lhs, const ::semantic_perception::SemanticSegmentation_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.image == rhs.image &&
    lhs.depth_image == rhs.depth_image &&
    lhs.segmentation_mask == rhs.segmentation_mask &&
    lhs.confidence_map == rhs.confidence_map &&
    lhs.class_names == rhs.class_names &&
    lhs.class_ids == rhs.class_ids &&
    lhs.class_colors == rhs.class_colors &&
    lhs.num_classes == rhs.num_classes &&
    lhs.mean_confidence == rhs.mean_confidence &&
    lhs.processing_time == rhs.processing_time &&
    lhs.camera_info == rhs.camera_info;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_perception::SemanticSegmentation_<ContainerAllocator1> & lhs, const ::semantic_perception::SemanticSegmentation_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_perception

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> >
{
  static const char* value()
  {
    return "d5376703ad7537c820b48f16495686c3";
  }

  static const char* value(const ::semantic_perception::SemanticSegmentation_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xd5376703ad7537c8ULL;
  static const uint64_t static_value2 = 0x20b48f16495686c3ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_perception/SemanticSegmentation";
  }

  static const char* value(const ::semantic_perception::SemanticSegmentation_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 语义分割结果消息\n"
"Header header\n"
"\n"
"# 原始图像信息\n"
"sensor_msgs/Image image              # 原始RGB图像\n"
"sensor_msgs/Image depth_image        # 深度图像 (如果可用)\n"
"\n"
"# 分割结果\n"
"sensor_msgs/Image segmentation_mask  # 语义分割掩码 (每个像素值对应类别ID)\n"
"sensor_msgs/Image confidence_map     # 置信度图 (每个像素的分割置信度)\n"
"\n"
"# 类别信息\n"
"string[] class_names                 # 类别名称列表\n"
"int32[] class_ids                    # 类别ID列表\n"
"int32[] class_colors                 # 类别颜色 (RGB打包为int32)\n"
"\n"
"# 统计信息\n"
"int32 num_classes                    # 检测到的类别数量\n"
"float32 mean_confidence              # 平均置信度\n"
"float32 processing_time              # 处理时间 (秒)\n"
"\n"
"# 相机信息\n"
"sensor_msgs/CameraInfo camera_info   # 相机内参信息\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: sensor_msgs/Image\n"
"# This message contains an uncompressed image\n"
"# (0, 0) is at top-left corner of image\n"
"#\n"
"\n"
"Header header        # Header timestamp should be acquisition time of image\n"
"                     # Header frame_id should be optical frame of camera\n"
"                     # origin of frame should be optical center of camera\n"
"                     # +x should point to the right in the image\n"
"                     # +y should point down in the image\n"
"                     # +z should point into to plane of the image\n"
"                     # If the frame_id here and the frame_id of the CameraInfo\n"
"                     # message associated with the image conflict\n"
"                     # the behavior is undefined\n"
"\n"
"uint32 height         # image height, that is, number of rows\n"
"uint32 width          # image width, that is, number of columns\n"
"\n"
"# The legal values for encoding are in file src/image_encodings.cpp\n"
"# If you want to standardize a new string format, join\n"
"# <EMAIL> and send an email proposing a new encoding.\n"
"\n"
"string encoding       # Encoding of pixels -- channel meaning, ordering, size\n"
"                      # taken from the list of strings in include/sensor_msgs/image_encodings.h\n"
"\n"
"uint8 is_bigendian    # is this data bigendian?\n"
"uint32 step           # Full row length in bytes\n"
"uint8[] data          # actual matrix data, size is (step * rows)\n"
"\n"
"================================================================================\n"
"MSG: sensor_msgs/CameraInfo\n"
"# This message defines meta information for a camera. It should be in a\n"
"# camera namespace on topic \"camera_info\" and accompanied by up to five\n"
"# image topics named:\n"
"#\n"
"#   image_raw - raw data from the camera driver, possibly Bayer encoded\n"
"#   image            - monochrome, distorted\n"
"#   image_color      - color, distorted\n"
"#   image_rect       - monochrome, rectified\n"
"#   image_rect_color - color, rectified\n"
"#\n"
"# The image_pipeline contains packages (image_proc, stereo_image_proc)\n"
"# for producing the four processed image topics from image_raw and\n"
"# camera_info. The meaning of the camera parameters are described in\n"
"# detail at http://www.ros.org/wiki/image_pipeline/CameraInfo.\n"
"#\n"
"# The image_geometry package provides a user-friendly interface to\n"
"# common operations using this meta information. If you want to, e.g.,\n"
"# project a 3d point into image coordinates, we strongly recommend\n"
"# using image_geometry.\n"
"#\n"
"# If the camera is uncalibrated, the matrices D, K, R, P should be left\n"
"# zeroed out. In particular, clients may assume that K[0] == 0.0\n"
"# indicates an uncalibrated camera.\n"
"\n"
"#######################################################################\n"
"#                     Image acquisition info                          #\n"
"#######################################################################\n"
"\n"
"# Time of image acquisition, camera coordinate frame ID\n"
"Header header    # Header timestamp should be acquisition time of image\n"
"                 # Header frame_id should be optical frame of camera\n"
"                 # origin of frame should be optical center of camera\n"
"                 # +x should point to the right in the image\n"
"                 # +y should point down in the image\n"
"                 # +z should point into the plane of the image\n"
"\n"
"\n"
"#######################################################################\n"
"#                      Calibration Parameters                         #\n"
"#######################################################################\n"
"# These are fixed during camera calibration. Their values will be the #\n"
"# same in all messages until the camera is recalibrated. Note that    #\n"
"# self-calibrating systems may \"recalibrate\" frequently.              #\n"
"#                                                                     #\n"
"# The internal parameters can be used to warp a raw (distorted) image #\n"
"# to:                                                                 #\n"
"#   1. An undistorted image (requires D and K)                        #\n"
"#   2. A rectified image (requires D, K, R)                           #\n"
"# The projection matrix P projects 3D points into the rectified image.#\n"
"#######################################################################\n"
"\n"
"# The image dimensions with which the camera was calibrated. Normally\n"
"# this will be the full camera resolution in pixels.\n"
"uint32 height\n"
"uint32 width\n"
"\n"
"# The distortion model used. Supported models are listed in\n"
"# sensor_msgs/distortion_models.h. For most cameras, \"plumb_bob\" - a\n"
"# simple model of radial and tangential distortion - is sufficient.\n"
"string distortion_model\n"
"\n"
"# The distortion parameters, size depending on the distortion model.\n"
"# For \"plumb_bob\", the 5 parameters are: (k1, k2, t1, t2, k3).\n"
"float64[] D\n"
"\n"
"# Intrinsic camera matrix for the raw (distorted) images.\n"
"#     [fx  0 cx]\n"
"# K = [ 0 fy cy]\n"
"#     [ 0  0  1]\n"
"# Projects 3D points in the camera coordinate frame to 2D pixel\n"
"# coordinates using the focal lengths (fx, fy) and principal point\n"
"# (cx, cy).\n"
"float64[9]  K # 3x3 row-major matrix\n"
"\n"
"# Rectification matrix (stereo cameras only)\n"
"# A rotation matrix aligning the camera coordinate system to the ideal\n"
"# stereo image plane so that epipolar lines in both stereo images are\n"
"# parallel.\n"
"float64[9]  R # 3x3 row-major matrix\n"
"\n"
"# Projection/camera matrix\n"
"#     [fx'  0  cx' Tx]\n"
"# P = [ 0  fy' cy' Ty]\n"
"#     [ 0   0   1   0]\n"
"# By convention, this matrix specifies the intrinsic (camera) matrix\n"
"#  of the processed (rectified) image. That is, the left 3x3 portion\n"
"#  is the normal camera intrinsic matrix for the rectified image.\n"
"# It projects 3D points in the camera coordinate frame to 2D pixel\n"
"#  coordinates using the focal lengths (fx', fy') and principal point\n"
"#  (cx', cy') - these may differ from the values in K.\n"
"# For monocular cameras, Tx = Ty = 0. Normally, monocular cameras will\n"
"#  also have R = the identity and P[1:3,1:3] = K.\n"
"# For a stereo pair, the fourth column [Tx Ty 0]' is related to the\n"
"#  position of the optical center of the second camera in the first\n"
"#  camera's frame. We assume Tz = 0 so both cameras are in the same\n"
"#  stereo image plane. The first camera always has Tx = Ty = 0. For\n"
"#  the right (second) camera of a horizontal stereo pair, Ty = 0 and\n"
"#  Tx = -fx' * B, where B is the baseline between the cameras.\n"
"# Given a 3D point [X Y Z]', the projection (x, y) of the point onto\n"
"#  the rectified image is given by:\n"
"#  [u v w]' = P * [X Y Z 1]'\n"
"#         x = u / w\n"
"#         y = v / w\n"
"#  This holds for both images of a stereo pair.\n"
"float64[12] P # 3x4 row-major matrix\n"
"\n"
"\n"
"#######################################################################\n"
"#                      Operational Parameters                         #\n"
"#######################################################################\n"
"# These define the image region actually captured by the camera       #\n"
"# driver. Although they affect the geometry of the output image, they #\n"
"# may be changed freely without recalibrating the camera.             #\n"
"#######################################################################\n"
"\n"
"# Binning refers here to any camera setting which combines rectangular\n"
"#  neighborhoods of pixels into larger \"super-pixels.\" It reduces the\n"
"#  resolution of the output image to\n"
"#  (width / binning_x) x (height / binning_y).\n"
"# The default values binning_x = binning_y = 0 is considered the same\n"
"#  as binning_x = binning_y = 1 (no subsampling).\n"
"uint32 binning_x\n"
"uint32 binning_y\n"
"\n"
"# Region of interest (subwindow of full camera resolution), given in\n"
"#  full resolution (unbinned) image coordinates. A particular ROI\n"
"#  always denotes the same window of pixels on the camera sensor,\n"
"#  regardless of binning settings.\n"
"# The default setting of roi (all values 0) is considered the same as\n"
"#  full resolution (roi.width = width, roi.height = height).\n"
"RegionOfInterest roi\n"
"\n"
"================================================================================\n"
"MSG: sensor_msgs/RegionOfInterest\n"
"# This message is used to specify a region of interest within an image.\n"
"#\n"
"# When used to specify the ROI setting of the camera when the image was\n"
"# taken, the height and width fields should either match the height and\n"
"# width fields for the associated image; or height = width = 0\n"
"# indicates that the full resolution image was captured.\n"
"\n"
"uint32 x_offset  # Leftmost pixel of the ROI\n"
"                 # (0 if the ROI includes the left edge of the image)\n"
"uint32 y_offset  # Topmost pixel of the ROI\n"
"                 # (0 if the ROI includes the top edge of the image)\n"
"uint32 height    # Height of ROI\n"
"uint32 width     # Width of ROI\n"
"\n"
"# True if a distinct rectified ROI should be calculated from the \"raw\"\n"
"# ROI in this message. Typically this should be False if the full image\n"
"# is captured (ROI not used), and True if a subwindow is captured (ROI\n"
"# used).\n"
"bool do_rectify\n"
;
  }

  static const char* value(const ::semantic_perception::SemanticSegmentation_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.image);
      stream.next(m.depth_image);
      stream.next(m.segmentation_mask);
      stream.next(m.confidence_map);
      stream.next(m.class_names);
      stream.next(m.class_ids);
      stream.next(m.class_colors);
      stream.next(m.num_classes);
      stream.next(m.mean_confidence);
      stream.next(m.processing_time);
      stream.next(m.camera_info);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SemanticSegmentation_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_perception::SemanticSegmentation_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_perception::SemanticSegmentation_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "image: ";
    Printer< ::sensor_msgs::Image_<ContainerAllocator> >::stream(s, indent + "  ", v.image);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "depth_image: ";
    Printer< ::sensor_msgs::Image_<ContainerAllocator> >::stream(s, indent + "  ", v.depth_image);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "segmentation_mask: ";
    Printer< ::sensor_msgs::Image_<ContainerAllocator> >::stream(s, indent + "  ", v.segmentation_mask);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence_map: ";
    Printer< ::sensor_msgs::Image_<ContainerAllocator> >::stream(s, indent + "  ", v.confidence_map);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_names: ";
    if (v.class_names.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_names.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.class_names[i]);
    }
    if (v.class_names.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_ids: ";
    if (v.class_ids.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_ids.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.class_ids[i]);
    }
    if (v.class_ids.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_colors: ";
    if (v.class_colors.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_colors.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.class_colors[i]);
    }
    if (v.class_colors.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "num_classes: ";
    Printer<int32_t>::stream(s, indent + "  ", v.num_classes);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "mean_confidence: ";
    Printer<float>::stream(s, indent + "  ", v.mean_confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "processing_time: ";
    Printer<float>::stream(s, indent + "  ", v.processing_time);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "camera_info: ";
    Printer< ::sensor_msgs::CameraInfo_<ContainerAllocator> >::stream(s, indent + "  ", v.camera_info);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_PERCEPTION_MESSAGE_SEMANTICSEGMENTATION_H
