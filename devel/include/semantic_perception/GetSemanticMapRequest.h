// Generated by gencpp from file semantic_perception/GetSemanticMapRequest.msg
// DO NOT EDIT!


#ifndef SEMANTIC_PERCEPTION_MESSAGE_GETSEMANTICMAPREQUEST_H
#define SEMANTIC_PERCEPTION_MESSAGE_GETSEMANTICMAPREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Point.h>
#include <geometry_msgs/Point.h>

namespace semantic_perception
{
template <class ContainerAllocator>
struct GetSemanticMapRequest_
{
  typedef GetSemanticMapRequest_<ContainerAllocator> Type;

  GetSemanticMapRequest_()
    : min_point()
    , max_point()
    , class_filter()
    , confidence_threshold(0.0)  {
    }
  GetSemanticMapRequest_(const ContainerAllocator& _alloc)
    : min_point(_alloc)
    , max_point(_alloc)
    , class_filter(_alloc)
    , confidence_threshold(0.0)  {
  (void)_alloc;
    }



   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _min_point_type;
  _min_point_type min_point;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _max_point_type;
  _max_point_type max_point;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _class_filter_type;
  _class_filter_type class_filter;

   typedef float _confidence_threshold_type;
  _confidence_threshold_type confidence_threshold;





  typedef boost::shared_ptr< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> const> ConstPtr;

}; // struct GetSemanticMapRequest_

typedef ::semantic_perception::GetSemanticMapRequest_<std::allocator<void> > GetSemanticMapRequest;

typedef boost::shared_ptr< ::semantic_perception::GetSemanticMapRequest > GetSemanticMapRequestPtr;
typedef boost::shared_ptr< ::semantic_perception::GetSemanticMapRequest const> GetSemanticMapRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator1> & lhs, const ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator2> & rhs)
{
  return lhs.min_point == rhs.min_point &&
    lhs.max_point == rhs.max_point &&
    lhs.class_filter == rhs.class_filter &&
    lhs.confidence_threshold == rhs.confidence_threshold;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator1> & lhs, const ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_perception

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "cf0fb5b24d8f199d03a5895b0c421bee";
  }

  static const char* value(const ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xcf0fb5b24d8f199dULL;
  static const uint64_t static_value2 = 0x03a5895b0c421beeULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_perception/GetSemanticMapRequest";
  }

  static const char* value(const ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 获取语义地图服务\n"
"# 请求\n"
"geometry_msgs/Point min_point    # 查询区域最小点\n"
"geometry_msgs/Point max_point    # 查询区域最大点\n"
"string[] class_filter           # 类别过滤器 (空表示所有类别)\n"
"float32 confidence_threshold    # 置信度阈值\n"
"\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.min_point);
      stream.next(m.max_point);
      stream.next(m.class_filter);
      stream.next(m.confidence_threshold);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetSemanticMapRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_perception::GetSemanticMapRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "min_point: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.min_point);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "max_point: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.max_point);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_filter: ";
    if (v.class_filter.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_filter.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.class_filter[i]);
    }
    if (v.class_filter.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence_threshold: ";
    Printer<float>::stream(s, indent + "  ", v.confidence_threshold);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_PERCEPTION_MESSAGE_GETSEMANTICMAPREQUEST_H
