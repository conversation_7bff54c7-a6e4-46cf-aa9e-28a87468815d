// Generated by gencpp from file semantic_perception/SemanticObject.msg
// DO NOT EDIT!


#ifndef SEMANTIC_PERCEPTION_MESSAGE_SEMANTICOBJECT_H
#define SEMANTIC_PERCEPTION_MESSAGE_SEMANTICOBJECT_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <sensor_msgs/Image.h>
#include <geometry_msgs/Point.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Quaternion.h>

namespace semantic_perception
{
template <class ContainerAllocator>
struct SemanticObject_
{
  typedef SemanticObject_<ContainerAllocator> Type;

  SemanticObject_()
    : header()
    , object_id()
    , class_name()
    , class_id(0)
    , confidence(0.0)
    , bbox_center_x(0.0)
    , bbox_center_y(0.0)
    , bbox_width(0.0)
    , bbox_height(0.0)
    , mask()
    , has_3d_info(false)
    , position()
    , size()
    , orientation()
    , is_static(false)
    , is_navigable(false)
    , is_obstacle(false)
    , is_fire(false)
    , is_dangerous(false)
    , track_id(0)
    , age(0)
    , velocity(0.0)
    , first_seen(0)
    , last_seen(0)  {
    }
  SemanticObject_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , object_id(_alloc)
    , class_name(_alloc)
    , class_id(0)
    , confidence(0.0)
    , bbox_center_x(0.0)
    , bbox_center_y(0.0)
    , bbox_width(0.0)
    , bbox_height(0.0)
    , mask(_alloc)
    , has_3d_info(false)
    , position(_alloc)
    , size(_alloc)
    , orientation(_alloc)
    , is_static(false)
    , is_navigable(false)
    , is_obstacle(false)
    , is_fire(false)
    , is_dangerous(false)
    , track_id(0)
    , age(0)
    , velocity(0.0)
    , first_seen(0)
    , last_seen(0)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _object_id_type;
  _object_id_type object_id;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _class_name_type;
  _class_name_type class_name;

   typedef int32_t _class_id_type;
  _class_id_type class_id;

   typedef float _confidence_type;
  _confidence_type confidence;

   typedef float _bbox_center_x_type;
  _bbox_center_x_type bbox_center_x;

   typedef float _bbox_center_y_type;
  _bbox_center_y_type bbox_center_y;

   typedef float _bbox_width_type;
  _bbox_width_type bbox_width;

   typedef float _bbox_height_type;
  _bbox_height_type bbox_height;

   typedef  ::sensor_msgs::Image_<ContainerAllocator>  _mask_type;
  _mask_type mask;

   typedef uint8_t _has_3d_info_type;
  _has_3d_info_type has_3d_info;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _position_type;
  _position_type position;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _size_type;
  _size_type size;

   typedef  ::geometry_msgs::Quaternion_<ContainerAllocator>  _orientation_type;
  _orientation_type orientation;

   typedef uint8_t _is_static_type;
  _is_static_type is_static;

   typedef uint8_t _is_navigable_type;
  _is_navigable_type is_navigable;

   typedef uint8_t _is_obstacle_type;
  _is_obstacle_type is_obstacle;

   typedef uint8_t _is_fire_type;
  _is_fire_type is_fire;

   typedef uint8_t _is_dangerous_type;
  _is_dangerous_type is_dangerous;

   typedef int32_t _track_id_type;
  _track_id_type track_id;

   typedef int32_t _age_type;
  _age_type age;

   typedef float _velocity_type;
  _velocity_type velocity;

   typedef uint32_t _first_seen_type;
  _first_seen_type first_seen;

   typedef uint32_t _last_seen_type;
  _last_seen_type last_seen;





  typedef boost::shared_ptr< ::semantic_perception::SemanticObject_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_perception::SemanticObject_<ContainerAllocator> const> ConstPtr;

}; // struct SemanticObject_

typedef ::semantic_perception::SemanticObject_<std::allocator<void> > SemanticObject;

typedef boost::shared_ptr< ::semantic_perception::SemanticObject > SemanticObjectPtr;
typedef boost::shared_ptr< ::semantic_perception::SemanticObject const> SemanticObjectConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_perception::SemanticObject_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_perception::SemanticObject_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_perception::SemanticObject_<ContainerAllocator1> & lhs, const ::semantic_perception::SemanticObject_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.object_id == rhs.object_id &&
    lhs.class_name == rhs.class_name &&
    lhs.class_id == rhs.class_id &&
    lhs.confidence == rhs.confidence &&
    lhs.bbox_center_x == rhs.bbox_center_x &&
    lhs.bbox_center_y == rhs.bbox_center_y &&
    lhs.bbox_width == rhs.bbox_width &&
    lhs.bbox_height == rhs.bbox_height &&
    lhs.mask == rhs.mask &&
    lhs.has_3d_info == rhs.has_3d_info &&
    lhs.position == rhs.position &&
    lhs.size == rhs.size &&
    lhs.orientation == rhs.orientation &&
    lhs.is_static == rhs.is_static &&
    lhs.is_navigable == rhs.is_navigable &&
    lhs.is_obstacle == rhs.is_obstacle &&
    lhs.is_fire == rhs.is_fire &&
    lhs.is_dangerous == rhs.is_dangerous &&
    lhs.track_id == rhs.track_id &&
    lhs.age == rhs.age &&
    lhs.velocity == rhs.velocity &&
    lhs.first_seen == rhs.first_seen &&
    lhs.last_seen == rhs.last_seen;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_perception::SemanticObject_<ContainerAllocator1> & lhs, const ::semantic_perception::SemanticObject_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_perception

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_perception::SemanticObject_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_perception::SemanticObject_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_perception::SemanticObject_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_perception::SemanticObject_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_perception::SemanticObject_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_perception::SemanticObject_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_perception::SemanticObject_<ContainerAllocator> >
{
  static const char* value()
  {
    return "50f98c86627bbf425de7be3967dd6abd";
  }

  static const char* value(const ::semantic_perception::SemanticObject_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x50f98c86627bbf42ULL;
  static const uint64_t static_value2 = 0x5de7be3967dd6abdULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_perception::SemanticObject_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_perception/SemanticObject";
  }

  static const char* value(const ::semantic_perception::SemanticObject_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_perception::SemanticObject_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 语义物体消息\n"
"Header header\n"
"\n"
"# 物体基本信息\n"
"string object_id                     # 物体唯一标识符\n"
"string class_name                    # 物体类别名称\n"
"int32 class_id                       # 物体类别ID\n"
"float32 confidence                   # 检测置信度 [0.0, 1.0]\n"
"\n"
"# 2D信息\n"
"float32 bbox_center_x                # 2D边界框中心X (像素坐标)\n"
"float32 bbox_center_y                # 2D边界框中心Y (像素坐标)\n"
"float32 bbox_width                   # 边界框宽度 (像素)\n"
"float32 bbox_height                  # 边界框高度 (像素)\n"
"sensor_msgs/Image mask               # 分割掩码 (可选)\n"
"\n"
"# 3D信息\n"
"bool has_3d_info                     # 是否有3D信息\n"
"geometry_msgs/Point position         # 3D位置 (世界坐标系)\n"
"geometry_msgs/Vector3 size           # 3D尺寸\n"
"geometry_msgs/Quaternion orientation # 3D方向\n"
"\n"
"# 语义属性\n"
"bool is_static                       # 是否为静态物体\n"
"bool is_navigable                    # 是否可通行\n"
"bool is_obstacle                     # 是否为障碍物\n"
"bool is_fire                         # 是否为火焰\n"
"bool is_dangerous                    # 是否危险\n"
"\n"
"# 追踪信息\n"
"int32 track_id                       # 追踪ID (-1表示未追踪)\n"
"int32 age                           # 物体年龄 (帧数)\n"
"float32 velocity                     # 运动速度 (m/s)\n"
"\n"
"# 时间戳\n"
"uint32 first_seen                   # 首次检测时间\n"
"uint32 last_seen                    # 最后检测时间\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: sensor_msgs/Image\n"
"# This message contains an uncompressed image\n"
"# (0, 0) is at top-left corner of image\n"
"#\n"
"\n"
"Header header        # Header timestamp should be acquisition time of image\n"
"                     # Header frame_id should be optical frame of camera\n"
"                     # origin of frame should be optical center of camera\n"
"                     # +x should point to the right in the image\n"
"                     # +y should point down in the image\n"
"                     # +z should point into to plane of the image\n"
"                     # If the frame_id here and the frame_id of the CameraInfo\n"
"                     # message associated with the image conflict\n"
"                     # the behavior is undefined\n"
"\n"
"uint32 height         # image height, that is, number of rows\n"
"uint32 width          # image width, that is, number of columns\n"
"\n"
"# The legal values for encoding are in file src/image_encodings.cpp\n"
"# If you want to standardize a new string format, join\n"
"# <EMAIL> and send an email proposing a new encoding.\n"
"\n"
"string encoding       # Encoding of pixels -- channel meaning, ordering, size\n"
"                      # taken from the list of strings in include/sensor_msgs/image_encodings.h\n"
"\n"
"uint8 is_bigendian    # is this data bigendian?\n"
"uint32 step           # Full row length in bytes\n"
"uint8[] data          # actual matrix data, size is (step * rows)\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_perception::SemanticObject_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_perception::SemanticObject_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.object_id);
      stream.next(m.class_name);
      stream.next(m.class_id);
      stream.next(m.confidence);
      stream.next(m.bbox_center_x);
      stream.next(m.bbox_center_y);
      stream.next(m.bbox_width);
      stream.next(m.bbox_height);
      stream.next(m.mask);
      stream.next(m.has_3d_info);
      stream.next(m.position);
      stream.next(m.size);
      stream.next(m.orientation);
      stream.next(m.is_static);
      stream.next(m.is_navigable);
      stream.next(m.is_obstacle);
      stream.next(m.is_fire);
      stream.next(m.is_dangerous);
      stream.next(m.track_id);
      stream.next(m.age);
      stream.next(m.velocity);
      stream.next(m.first_seen);
      stream.next(m.last_seen);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SemanticObject_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_perception::SemanticObject_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_perception::SemanticObject_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "object_id: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.object_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.class_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.class_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<float>::stream(s, indent + "  ", v.confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "bbox_center_x: ";
    Printer<float>::stream(s, indent + "  ", v.bbox_center_x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "bbox_center_y: ";
    Printer<float>::stream(s, indent + "  ", v.bbox_center_y);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "bbox_width: ";
    Printer<float>::stream(s, indent + "  ", v.bbox_width);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "bbox_height: ";
    Printer<float>::stream(s, indent + "  ", v.bbox_height);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "mask: ";
    Printer< ::sensor_msgs::Image_<ContainerAllocator> >::stream(s, indent + "  ", v.mask);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "has_3d_info: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.has_3d_info);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "position: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.position);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "size: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.size);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "orientation: ";
    Printer< ::geometry_msgs::Quaternion_<ContainerAllocator> >::stream(s, indent + "  ", v.orientation);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_static: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_static);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_navigable: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_navigable);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_obstacle: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_obstacle);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_fire: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_fire);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_dangerous: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_dangerous);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "track_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.track_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "age: ";
    Printer<int32_t>::stream(s, indent + "  ", v.age);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "velocity: ";
    Printer<float>::stream(s, indent + "  ", v.velocity);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "first_seen: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.first_seen);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "last_seen: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.last_seen);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_PERCEPTION_MESSAGE_SEMANTICOBJECT_H
