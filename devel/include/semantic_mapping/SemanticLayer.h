// Generated by gencpp from file semantic_mapping/SemanticLayer.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_SEMANTICLAYER_H
#define SEMANTIC_MAPPING_MESSAGE_SEMANTICLAYER_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/Pose.h>

namespace semantic_mapping
{
template <class ContainerAllocator>
struct SemanticLayer_
{
  typedef SemanticLayer_<ContainerAllocator> Type;

  SemanticLayer_()
    : header()
    , layer_name()
    , layer_type()
    , layer_id(0)
    , origin()
    , resolution(0.0)
    , width(0)
    , height(0)
    , data()
    , mask()
    , min_value(0.0)
    , max_value(0.0)
    , encoding()
    , timestamp(0)  {
    }
  SemanticLayer_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , layer_name(_alloc)
    , layer_type(_alloc)
    , layer_id(0)
    , origin(_alloc)
    , resolution(0.0)
    , width(0)
    , height(0)
    , data(_alloc)
    , mask(_alloc)
    , min_value(0.0)
    , max_value(0.0)
    , encoding(_alloc)
    , timestamp(0)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _layer_name_type;
  _layer_name_type layer_name;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _layer_type_type;
  _layer_type_type layer_type;

   typedef int32_t _layer_id_type;
  _layer_id_type layer_id;

   typedef  ::geometry_msgs::Pose_<ContainerAllocator>  _origin_type;
  _origin_type origin;

   typedef float _resolution_type;
  _resolution_type resolution;

   typedef int32_t _width_type;
  _width_type width;

   typedef int32_t _height_type;
  _height_type height;

   typedef std::vector<float, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<float>> _data_type;
  _data_type data;

   typedef std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>> _mask_type;
  _mask_type mask;

   typedef float _min_value_type;
  _min_value_type min_value;

   typedef float _max_value_type;
  _max_value_type max_value;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _encoding_type;
  _encoding_type encoding;

   typedef uint32_t _timestamp_type;
  _timestamp_type timestamp;





  typedef boost::shared_ptr< ::semantic_mapping::SemanticLayer_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_mapping::SemanticLayer_<ContainerAllocator> const> ConstPtr;

}; // struct SemanticLayer_

typedef ::semantic_mapping::SemanticLayer_<std::allocator<void> > SemanticLayer;

typedef boost::shared_ptr< ::semantic_mapping::SemanticLayer > SemanticLayerPtr;
typedef boost::shared_ptr< ::semantic_mapping::SemanticLayer const> SemanticLayerConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_mapping::SemanticLayer_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_mapping::SemanticLayer_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_mapping::SemanticLayer_<ContainerAllocator1> & lhs, const ::semantic_mapping::SemanticLayer_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.layer_name == rhs.layer_name &&
    lhs.layer_type == rhs.layer_type &&
    lhs.layer_id == rhs.layer_id &&
    lhs.origin == rhs.origin &&
    lhs.resolution == rhs.resolution &&
    lhs.width == rhs.width &&
    lhs.height == rhs.height &&
    lhs.data == rhs.data &&
    lhs.mask == rhs.mask &&
    lhs.min_value == rhs.min_value &&
    lhs.max_value == rhs.max_value &&
    lhs.encoding == rhs.encoding &&
    lhs.timestamp == rhs.timestamp;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_mapping::SemanticLayer_<ContainerAllocator1> & lhs, const ::semantic_mapping::SemanticLayer_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_mapping

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::SemanticLayer_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::SemanticLayer_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::SemanticLayer_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::SemanticLayer_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::SemanticLayer_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::SemanticLayer_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_mapping::SemanticLayer_<ContainerAllocator> >
{
  static const char* value()
  {
    return "a57abbb1ca0b0891e590fd7c6cf4d209";
  }

  static const char* value(const ::semantic_mapping::SemanticLayer_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xa57abbb1ca0b0891ULL;
  static const uint64_t static_value2 = 0xe590fd7c6cf4d209ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_mapping::SemanticLayer_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_mapping/SemanticLayer";
  }

  static const char* value(const ::semantic_mapping::SemanticLayer_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_mapping::SemanticLayer_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 语义层消息\n"
"Header header\n"
"\n"
"# 层基本信息\n"
"string layer_name                    # 层名称\n"
"string layer_type                    # 层类型 (occupancy, semantic, fire, etc.)\n"
"int32 layer_id                       # 层ID\n"
"\n"
"# 网格信息\n"
"geometry_msgs/Pose origin            # 网格原点\n"
"float32 resolution                   # 分辨率 (米/像素)\n"
"int32 width                         # 宽度 (像素)\n"
"int32 height                        # 高度 (像素)\n"
"\n"
"# 数据\n"
"float32[] data                      # 层数据 (按行优先顺序)\n"
"uint8[] mask                        # 有效数据掩码\n"
"\n"
"# 元数据\n"
"float32 min_value                   # 最小值\n"
"float32 max_value                   # 最大值\n"
"string encoding                     # 数据编码类型\n"
"uint32 timestamp                    # 时间戳\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_mapping::SemanticLayer_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_mapping::SemanticLayer_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.layer_name);
      stream.next(m.layer_type);
      stream.next(m.layer_id);
      stream.next(m.origin);
      stream.next(m.resolution);
      stream.next(m.width);
      stream.next(m.height);
      stream.next(m.data);
      stream.next(m.mask);
      stream.next(m.min_value);
      stream.next(m.max_value);
      stream.next(m.encoding);
      stream.next(m.timestamp);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SemanticLayer_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_mapping::SemanticLayer_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_mapping::SemanticLayer_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "layer_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.layer_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "layer_type: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.layer_type);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "layer_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.layer_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "origin: ";
    Printer< ::geometry_msgs::Pose_<ContainerAllocator> >::stream(s, indent + "  ", v.origin);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "resolution: ";
    Printer<float>::stream(s, indent + "  ", v.resolution);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "width: ";
    Printer<int32_t>::stream(s, indent + "  ", v.width);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "height: ";
    Printer<int32_t>::stream(s, indent + "  ", v.height);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "data: ";
    if (v.data.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.data.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.data[i]);
    }
    if (v.data.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "mask: ";
    if (v.mask.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.mask.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<uint8_t>::stream(s, true ? std::string() : indent + "    ", v.mask[i]);
    }
    if (v.mask.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "min_value: ";
    Printer<float>::stream(s, indent + "  ", v.min_value);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "max_value: ";
    Printer<float>::stream(s, indent + "  ", v.max_value);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "encoding: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.encoding);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "timestamp: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.timestamp);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_SEMANTICLAYER_H
