// Generated by gencpp from file semantic_mapping/UpdateSemanticMapResponse.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_UPDATESEMANTICMAPRESPONSE_H
#define SEMANTIC_MAPPING_MESSAGE_UPDATESEMANTICMAPRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace semantic_mapping
{
template <class ContainerAllocator>
struct UpdateSemanticMapResponse_
{
  typedef UpdateSemanticMapResponse_<ContainerAllocator> Type;

  UpdateSemanticMapResponse_()
    : success(false)
    , message()
    , updated_objects(0)
    , total_objects(0)  {
    }
  UpdateSemanticMapResponse_(const ContainerAllocator& _alloc)
    : success(false)
    , message(_alloc)
    , updated_objects(0)
    , total_objects(0)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _message_type;
  _message_type message;

   typedef int32_t _updated_objects_type;
  _updated_objects_type updated_objects;

   typedef int32_t _total_objects_type;
  _total_objects_type total_objects;





  typedef boost::shared_ptr< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> const> ConstPtr;

}; // struct UpdateSemanticMapResponse_

typedef ::semantic_mapping::UpdateSemanticMapResponse_<std::allocator<void> > UpdateSemanticMapResponse;

typedef boost::shared_ptr< ::semantic_mapping::UpdateSemanticMapResponse > UpdateSemanticMapResponsePtr;
typedef boost::shared_ptr< ::semantic_mapping::UpdateSemanticMapResponse const> UpdateSemanticMapResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator1> & lhs, const ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success &&
    lhs.message == rhs.message &&
    lhs.updated_objects == rhs.updated_objects &&
    lhs.total_objects == rhs.total_objects;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator1> & lhs, const ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_mapping

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "3db579641babf0519e5e29b7601d78cd";
  }

  static const char* value(const ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x3db579641babf051ULL;
  static const uint64_t static_value2 = 0x9e5e29b7601d78cdULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_mapping/UpdateSemanticMapResponse";
  }

  static const char* value(const ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"# 响应\n"
"bool success                        # 是否成功\n"
"string message                      # 状态消息\n"
"int32 updated_objects              # 更新的物体数量\n"
"int32 total_objects                # 总物体数量\n"
"\n"
;
  }

  static const char* value(const ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
      stream.next(m.message);
      stream.next(m.updated_objects);
      stream.next(m.total_objects);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct UpdateSemanticMapResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_mapping::UpdateSemanticMapResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "message: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.message);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "updated_objects: ";
    Printer<int32_t>::stream(s, indent + "  ", v.updated_objects);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "total_objects: ";
    Printer<int32_t>::stream(s, indent + "  ", v.total_objects);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_UPDATESEMANTICMAPRESPONSE_H
