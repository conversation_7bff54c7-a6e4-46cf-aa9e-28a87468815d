// Generated by gencpp from file semantic_mapping/GetSemanticMapRequest.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_GETSEMANTICMAPREQUEST_H
#define SEMANTIC_MAPPING_MESSAGE_GETSEMANTICMAPREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Point.h>
#include <geometry_msgs/Point.h>

namespace semantic_mapping
{
template <class ContainerAllocator>
struct GetSemanticMapRequest_
{
  typedef GetSemanticMapRequest_<ContainerAllocator> Type;

  GetSemanticMapRequest_()
    : min_point()
    , max_point()
    , class_filter()
    , confidence_threshold(0.0)
    , include_fire_locations(false)
    , include_navigable_areas(false)  {
    }
  GetSemanticMapRequest_(const ContainerAllocator& _alloc)
    : min_point(_alloc)
    , max_point(_alloc)
    , class_filter(_alloc)
    , confidence_threshold(0.0)
    , include_fire_locations(false)
    , include_navigable_areas(false)  {
  (void)_alloc;
    }



   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _min_point_type;
  _min_point_type min_point;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _max_point_type;
  _max_point_type max_point;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _class_filter_type;
  _class_filter_type class_filter;

   typedef float _confidence_threshold_type;
  _confidence_threshold_type confidence_threshold;

   typedef uint8_t _include_fire_locations_type;
  _include_fire_locations_type include_fire_locations;

   typedef uint8_t _include_navigable_areas_type;
  _include_navigable_areas_type include_navigable_areas;





  typedef boost::shared_ptr< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> const> ConstPtr;

}; // struct GetSemanticMapRequest_

typedef ::semantic_mapping::GetSemanticMapRequest_<std::allocator<void> > GetSemanticMapRequest;

typedef boost::shared_ptr< ::semantic_mapping::GetSemanticMapRequest > GetSemanticMapRequestPtr;
typedef boost::shared_ptr< ::semantic_mapping::GetSemanticMapRequest const> GetSemanticMapRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator1> & lhs, const ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator2> & rhs)
{
  return lhs.min_point == rhs.min_point &&
    lhs.max_point == rhs.max_point &&
    lhs.class_filter == rhs.class_filter &&
    lhs.confidence_threshold == rhs.confidence_threshold &&
    lhs.include_fire_locations == rhs.include_fire_locations &&
    lhs.include_navigable_areas == rhs.include_navigable_areas;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator1> & lhs, const ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_mapping

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "a037ac566c836db6759d0368c9ed1881";
  }

  static const char* value(const ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xa037ac566c836db6ULL;
  static const uint64_t static_value2 = 0x759d0368c9ed1881ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_mapping/GetSemanticMapRequest";
  }

  static const char* value(const ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 获取语义地图服务\n"
"# 请求\n"
"geometry_msgs/Point min_point    # 查询区域最小点\n"
"geometry_msgs/Point max_point    # 查询区域最大点\n"
"string[] class_filter           # 类别过滤器 (空表示所有类别)\n"
"float32 confidence_threshold    # 置信度阈值\n"
"bool include_fire_locations     # 是否包含火焰位置\n"
"bool include_navigable_areas    # 是否包含可通行区域\n"
"\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.min_point);
      stream.next(m.max_point);
      stream.next(m.class_filter);
      stream.next(m.confidence_threshold);
      stream.next(m.include_fire_locations);
      stream.next(m.include_navigable_areas);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetSemanticMapRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_mapping::GetSemanticMapRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "min_point: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.min_point);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "max_point: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.max_point);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_filter: ";
    if (v.class_filter.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_filter.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.class_filter[i]);
    }
    if (v.class_filter.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence_threshold: ";
    Printer<float>::stream(s, indent + "  ", v.confidence_threshold);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "include_fire_locations: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.include_fire_locations);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "include_navigable_areas: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.include_navigable_areas);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_GETSEMANTICMAPREQUEST_H
