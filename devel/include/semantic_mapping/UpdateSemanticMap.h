// Generated by gencpp from file semantic_mapping/UpdateSemanticMap.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_UPDATESEMANTICMAP_H
#define SEMANTIC_MAPPING_MESSAGE_UPDATESEMANTICMAP_H

#include <ros/service_traits.h>


#include <semantic_mapping/UpdateSemanticMapRequest.h>
#include <semantic_mapping/UpdateSemanticMapResponse.h>


namespace semantic_mapping
{

struct UpdateSemanticMap
{

typedef UpdateSemanticMapRequest Request;
typedef UpdateSemanticMapResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct UpdateSemanticMap
} // namespace semantic_mapping


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::semantic_mapping::UpdateSemanticMap > {
  static const char* value()
  {
    return "ecc1e27ca9fb7c744a878beec00353ac";
  }

  static const char* value(const ::semantic_mapping::UpdateSemanticMap&) { return value(); }
};

template<>
struct DataType< ::semantic_mapping::UpdateSemanticMap > {
  static const char* value()
  {
    return "semantic_mapping/UpdateSemanticMap";
  }

  static const char* value(const ::semantic_mapping::UpdateSemanticMap&) { return value(); }
};


// service_traits::MD5Sum< ::semantic_mapping::UpdateSemanticMapRequest> should match
// service_traits::MD5Sum< ::semantic_mapping::UpdateSemanticMap >
template<>
struct MD5Sum< ::semantic_mapping::UpdateSemanticMapRequest>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_mapping::UpdateSemanticMap >::value();
  }
  static const char* value(const ::semantic_mapping::UpdateSemanticMapRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_mapping::UpdateSemanticMapRequest> should match
// service_traits::DataType< ::semantic_mapping::UpdateSemanticMap >
template<>
struct DataType< ::semantic_mapping::UpdateSemanticMapRequest>
{
  static const char* value()
  {
    return DataType< ::semantic_mapping::UpdateSemanticMap >::value();
  }
  static const char* value(const ::semantic_mapping::UpdateSemanticMapRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::semantic_mapping::UpdateSemanticMapResponse> should match
// service_traits::MD5Sum< ::semantic_mapping::UpdateSemanticMap >
template<>
struct MD5Sum< ::semantic_mapping::UpdateSemanticMapResponse>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_mapping::UpdateSemanticMap >::value();
  }
  static const char* value(const ::semantic_mapping::UpdateSemanticMapResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_mapping::UpdateSemanticMapResponse> should match
// service_traits::DataType< ::semantic_mapping::UpdateSemanticMap >
template<>
struct DataType< ::semantic_mapping::UpdateSemanticMapResponse>
{
  static const char* value()
  {
    return DataType< ::semantic_mapping::UpdateSemanticMap >::value();
  }
  static const char* value(const ::semantic_mapping::UpdateSemanticMapResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_UPDATESEMANTICMAP_H
