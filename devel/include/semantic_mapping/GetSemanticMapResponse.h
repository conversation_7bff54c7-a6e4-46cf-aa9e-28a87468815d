// Generated by gencpp from file semantic_mapping/GetSemanticMapResponse.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_GETSEMANTICMAPRESPONSE_H
#define SEMANTIC_MAPPING_MESSAGE_GETSEMANTICMAPRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <semantic_mapping/SemanticMap.h>
#include <semantic_mapping/ObjectInstance.h>

namespace semantic_mapping
{
template <class ContainerAllocator>
struct GetSemanticMapResponse_
{
  typedef GetSemanticMapResponse_<ContainerAllocator> Type;

  GetSemanticMapResponse_()
    : success(false)
    , message()
    , semantic_map()
    , objects()
    , total_objects(0)  {
    }
  GetSemanticMapResponse_(const ContainerAllocator& _alloc)
    : success(false)
    , message(_alloc)
    , semantic_map(_alloc)
    , objects(_alloc)
    , total_objects(0)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _message_type;
  _message_type message;

   typedef  ::semantic_mapping::SemanticMap_<ContainerAllocator>  _semantic_map_type;
  _semantic_map_type semantic_map;

   typedef std::vector< ::semantic_mapping::ObjectInstance_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >> _objects_type;
  _objects_type objects;

   typedef int32_t _total_objects_type;
  _total_objects_type total_objects;





  typedef boost::shared_ptr< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> const> ConstPtr;

}; // struct GetSemanticMapResponse_

typedef ::semantic_mapping::GetSemanticMapResponse_<std::allocator<void> > GetSemanticMapResponse;

typedef boost::shared_ptr< ::semantic_mapping::GetSemanticMapResponse > GetSemanticMapResponsePtr;
typedef boost::shared_ptr< ::semantic_mapping::GetSemanticMapResponse const> GetSemanticMapResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator1> & lhs, const ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success &&
    lhs.message == rhs.message &&
    lhs.semantic_map == rhs.semantic_map &&
    lhs.objects == rhs.objects &&
    lhs.total_objects == rhs.total_objects;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator1> & lhs, const ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_mapping

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "a6a34fa368af017e923c509953962d68";
  }

  static const char* value(const ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xa6a34fa368af017eULL;
  static const uint64_t static_value2 = 0x923c509953962d68ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_mapping/GetSemanticMapResponse";
  }

  static const char* value(const ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"# 响应\n"
"bool success                    # 是否成功\n"
"string message                  # 状态消息\n"
"SemanticMap semantic_map        # 语义地图数据\n"
"ObjectInstance[] objects        # 物体实例列表\n"
"int32 total_objects            # 总物体数量\n"
"\n"
"\n"
"================================================================================\n"
"MSG: semantic_mapping/SemanticMap\n"
"# 语义地图消息\n"
"Header header\n"
"\n"
"# 地图元信息\n"
"string frame_id                      # 坐标系ID\n"
"geometry_msgs/Pose origin            # 地图原点位置\n"
"float32 resolution                   # 体素分辨率 (米)\n"
"geometry_msgs/Vector3 size           # 地图尺寸 (米)\n"
"int32[] dimensions                   # 网格维度 [x, y, z]\n"
"\n"
"# 体素数据\n"
"SemanticVoxel[] voxels              # 语义体素数组\n"
"\n"
"# 类别信息\n"
"string[] class_names                 # 类别名称列表\n"
"int32[] class_ids                    # 类别ID列表\n"
"int32[] class_colors                 # 类别颜色 (RGB)\n"
"\n"
"# 统计信息\n"
"int32 total_voxels                   # 总体素数量\n"
"int32 occupied_voxels                # 占用体素数量\n"
"int32 semantic_voxels                # 有语义标注的体素数量\n"
"float32 map_confidence               # 整体地图置信度\n"
"\n"
"# 特殊区域\n"
"geometry_msgs/Point[] fire_locations # 火焰位置列表\n"
"geometry_msgs/Point[] obstacle_centers # 障碍物中心列表\n"
"geometry_msgs/Polygon[] navigable_areas # 可通行区域\n"
"\n"
"# 更新信息\n"
"uint32 last_update_time             # 最后更新时间\n"
"uint32 version                      # 地图版本号\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"================================================================================\n"
"MSG: semantic_mapping/SemanticVoxel\n"
"# 语义体素消息\n"
"# 体素位置 (网格坐标)\n"
"int32 x\n"
"int32 y  \n"
"int32 z\n"
"\n"
"# 几何信息\n"
"float32 occupancy_probability        # 占用概率 [0.0, 1.0]\n"
"float32 tsdf_value                   # TSDF值\n"
"float32 weight                       # 融合权重\n"
"\n"
"# 语义信息\n"
"int32 semantic_class                 # 主要语义类别ID\n"
"float32 semantic_confidence          # 语义置信度\n"
"int32[] class_distribution           # 各类别的观测次数\n"
"float32[] class_probabilities        # 各类别的概率分布\n"
"\n"
"# 颜色信息\n"
"uint8 r                             # 红色分量 [0, 255]\n"
"uint8 g                             # 绿色分量 [0, 255] \n"
"uint8 b                             # 蓝色分量 [0, 255]\n"
"\n"
"# 特殊属性\n"
"bool is_fire                        # 是否包含火焰\n"
"bool is_obstacle                    # 是否为障碍物\n"
"bool is_navigable                   # 是否可通行\n"
"bool is_dynamic                     # 是否为动态物体\n"
"\n"
"# 更新信息\n"
"uint32 last_update_time             # 最后更新时间戳\n"
"uint32 observation_count            # 观测次数\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Polygon\n"
"#A specification of a polygon where the first and last points are assumed to be connected\n"
"Point32[] points\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point32\n"
"# This contains the position of a point in free space(with 32 bits of precision).\n"
"# It is recommeded to use Point wherever possible instead of Point32.  \n"
"# \n"
"# This recommendation is to promote interoperability.  \n"
"#\n"
"# This message is designed to take up less space when sending\n"
"# lots of points at once, as in the case of a PointCloud.  \n"
"\n"
"float32 x\n"
"float32 y\n"
"float32 z\n"
"================================================================================\n"
"MSG: semantic_mapping/ObjectInstance\n"
"# 物体实例消息\n"
"Header header\n"
"\n"
"# 实例基本信息\n"
"string instance_id                   # 实例唯一标识符\n"
"string class_name                    # 物体类别名称\n"
"int32 class_id                       # 物体类别ID\n"
"float32 confidence                   # 实例置信度 [0.0, 1.0]\n"
"\n"
"# 几何信息\n"
"geometry_msgs/Point centroid         # 质心位置\n"
"geometry_msgs/Vector3 bounding_box   # 边界框尺寸\n"
"geometry_msgs/Quaternion orientation # 方向\n"
"\n"
"# 体素信息\n"
"SemanticVoxel[] voxels              # 组成实例的体素列表\n"
"int32 voxel_count                   # 体素数量\n"
"float32 volume                      # 体积 (立方米)\n"
"\n"
"# 语义属性\n"
"bool is_complete                    # 是否完整观测\n"
"bool is_static                      # 是否静态\n"
"bool is_navigable                   # 是否可通行\n"
"bool is_obstacle                    # 是否为障碍物\n"
"bool is_fire                        # 是否为火焰\n"
"\n"
"# 时间信息\n"
"uint32 first_observed              # 首次观测时间\n"
"uint32 last_updated                # 最后更新时间\n"
"int32 observation_count            # 观测次数\n"
;
  }

  static const char* value(const ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
      stream.next(m.message);
      stream.next(m.semantic_map);
      stream.next(m.objects);
      stream.next(m.total_objects);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetSemanticMapResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_mapping::GetSemanticMapResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "message: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.message);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "semantic_map: ";
    Printer< ::semantic_mapping::SemanticMap_<ContainerAllocator> >::stream(s, indent + "  ", v.semantic_map);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "objects: ";
    if (v.objects.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.objects.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.objects[i]);
    }
    if (v.objects.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "total_objects: ";
    Printer<int32_t>::stream(s, indent + "  ", v.total_objects);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_GETSEMANTICMAPRESPONSE_H
