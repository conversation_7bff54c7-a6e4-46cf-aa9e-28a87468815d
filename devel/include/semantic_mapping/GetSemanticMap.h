// Generated by gencpp from file semantic_mapping/GetSemanticMap.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_GETSEMANTICMAP_H
#define SEMANTIC_MAPPING_MESSAGE_GETSEMANTICMAP_H

#include <ros/service_traits.h>


#include <semantic_mapping/GetSemanticMapRequest.h>
#include <semantic_mapping/GetSemanticMapResponse.h>


namespace semantic_mapping
{

struct GetSemanticMap
{

typedef GetSemanticMapRequest Request;
typedef GetSemanticMapResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetSemanticMap
} // namespace semantic_mapping


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::semantic_mapping::GetSemanticMap > {
  static const char* value()
  {
    return "561962b9895686d41e14b61a2ada3782";
  }

  static const char* value(const ::semantic_mapping::GetSemanticMap&) { return value(); }
};

template<>
struct DataType< ::semantic_mapping::GetSemanticMap > {
  static const char* value()
  {
    return "semantic_mapping/GetSemanticMap";
  }

  static const char* value(const ::semantic_mapping::GetSemanticMap&) { return value(); }
};


// service_traits::MD5Sum< ::semantic_mapping::GetSemanticMapRequest> should match
// service_traits::MD5Sum< ::semantic_mapping::GetSemanticMap >
template<>
struct MD5Sum< ::semantic_mapping::GetSemanticMapRequest>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_mapping::GetSemanticMap >::value();
  }
  static const char* value(const ::semantic_mapping::GetSemanticMapRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_mapping::GetSemanticMapRequest> should match
// service_traits::DataType< ::semantic_mapping::GetSemanticMap >
template<>
struct DataType< ::semantic_mapping::GetSemanticMapRequest>
{
  static const char* value()
  {
    return DataType< ::semantic_mapping::GetSemanticMap >::value();
  }
  static const char* value(const ::semantic_mapping::GetSemanticMapRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::semantic_mapping::GetSemanticMapResponse> should match
// service_traits::MD5Sum< ::semantic_mapping::GetSemanticMap >
template<>
struct MD5Sum< ::semantic_mapping::GetSemanticMapResponse>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_mapping::GetSemanticMap >::value();
  }
  static const char* value(const ::semantic_mapping::GetSemanticMapResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_mapping::GetSemanticMapResponse> should match
// service_traits::DataType< ::semantic_mapping::GetSemanticMap >
template<>
struct DataType< ::semantic_mapping::GetSemanticMapResponse>
{
  static const char* value()
  {
    return DataType< ::semantic_mapping::GetSemanticMap >::value();
  }
  static const char* value(const ::semantic_mapping::GetSemanticMapResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_GETSEMANTICMAP_H
