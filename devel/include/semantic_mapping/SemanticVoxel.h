// Generated by gencpp from file semantic_mapping/SemanticVoxel.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_SEMANTICVOXEL_H
#define SEMANTIC_MAPPING_MESSAGE_SEMANTICVOXEL_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace semantic_mapping
{
template <class ContainerAllocator>
struct SemanticVoxel_
{
  typedef SemanticVoxel_<ContainerAllocator> Type;

  SemanticVoxel_()
    : x(0)
    , y(0)
    , z(0)
    , occupancy_probability(0.0)
    , tsdf_value(0.0)
    , weight(0.0)
    , semantic_class(0)
    , semantic_confidence(0.0)
    , class_distribution()
    , class_probabilities()
    , r(0)
    , g(0)
    , b(0)
    , is_fire(false)
    , is_obstacle(false)
    , is_navigable(false)
    , is_dynamic(false)
    , last_update_time(0)
    , observation_count(0)  {
    }
  SemanticVoxel_(const ContainerAllocator& _alloc)
    : x(0)
    , y(0)
    , z(0)
    , occupancy_probability(0.0)
    , tsdf_value(0.0)
    , weight(0.0)
    , semantic_class(0)
    , semantic_confidence(0.0)
    , class_distribution(_alloc)
    , class_probabilities(_alloc)
    , r(0)
    , g(0)
    , b(0)
    , is_fire(false)
    , is_obstacle(false)
    , is_navigable(false)
    , is_dynamic(false)
    , last_update_time(0)
    , observation_count(0)  {
  (void)_alloc;
    }



   typedef int32_t _x_type;
  _x_type x;

   typedef int32_t _y_type;
  _y_type y;

   typedef int32_t _z_type;
  _z_type z;

   typedef float _occupancy_probability_type;
  _occupancy_probability_type occupancy_probability;

   typedef float _tsdf_value_type;
  _tsdf_value_type tsdf_value;

   typedef float _weight_type;
  _weight_type weight;

   typedef int32_t _semantic_class_type;
  _semantic_class_type semantic_class;

   typedef float _semantic_confidence_type;
  _semantic_confidence_type semantic_confidence;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _class_distribution_type;
  _class_distribution_type class_distribution;

   typedef std::vector<float, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<float>> _class_probabilities_type;
  _class_probabilities_type class_probabilities;

   typedef uint8_t _r_type;
  _r_type r;

   typedef uint8_t _g_type;
  _g_type g;

   typedef uint8_t _b_type;
  _b_type b;

   typedef uint8_t _is_fire_type;
  _is_fire_type is_fire;

   typedef uint8_t _is_obstacle_type;
  _is_obstacle_type is_obstacle;

   typedef uint8_t _is_navigable_type;
  _is_navigable_type is_navigable;

   typedef uint8_t _is_dynamic_type;
  _is_dynamic_type is_dynamic;

   typedef uint32_t _last_update_time_type;
  _last_update_time_type last_update_time;

   typedef uint32_t _observation_count_type;
  _observation_count_type observation_count;





  typedef boost::shared_ptr< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> const> ConstPtr;

}; // struct SemanticVoxel_

typedef ::semantic_mapping::SemanticVoxel_<std::allocator<void> > SemanticVoxel;

typedef boost::shared_ptr< ::semantic_mapping::SemanticVoxel > SemanticVoxelPtr;
typedef boost::shared_ptr< ::semantic_mapping::SemanticVoxel const> SemanticVoxelConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_mapping::SemanticVoxel_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_mapping::SemanticVoxel_<ContainerAllocator1> & lhs, const ::semantic_mapping::SemanticVoxel_<ContainerAllocator2> & rhs)
{
  return lhs.x == rhs.x &&
    lhs.y == rhs.y &&
    lhs.z == rhs.z &&
    lhs.occupancy_probability == rhs.occupancy_probability &&
    lhs.tsdf_value == rhs.tsdf_value &&
    lhs.weight == rhs.weight &&
    lhs.semantic_class == rhs.semantic_class &&
    lhs.semantic_confidence == rhs.semantic_confidence &&
    lhs.class_distribution == rhs.class_distribution &&
    lhs.class_probabilities == rhs.class_probabilities &&
    lhs.r == rhs.r &&
    lhs.g == rhs.g &&
    lhs.b == rhs.b &&
    lhs.is_fire == rhs.is_fire &&
    lhs.is_obstacle == rhs.is_obstacle &&
    lhs.is_navigable == rhs.is_navigable &&
    lhs.is_dynamic == rhs.is_dynamic &&
    lhs.last_update_time == rhs.last_update_time &&
    lhs.observation_count == rhs.observation_count;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_mapping::SemanticVoxel_<ContainerAllocator1> & lhs, const ::semantic_mapping::SemanticVoxel_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_mapping

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >
{
  static const char* value()
  {
    return "d0b01e369b22cb73e97ef48672c9c090";
  }

  static const char* value(const ::semantic_mapping::SemanticVoxel_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xd0b01e369b22cb73ULL;
  static const uint64_t static_value2 = 0xe97ef48672c9c090ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_mapping/SemanticVoxel";
  }

  static const char* value(const ::semantic_mapping::SemanticVoxel_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 语义体素消息\n"
"# 体素位置 (网格坐标)\n"
"int32 x\n"
"int32 y  \n"
"int32 z\n"
"\n"
"# 几何信息\n"
"float32 occupancy_probability        # 占用概率 [0.0, 1.0]\n"
"float32 tsdf_value                   # TSDF值\n"
"float32 weight                       # 融合权重\n"
"\n"
"# 语义信息\n"
"int32 semantic_class                 # 主要语义类别ID\n"
"float32 semantic_confidence          # 语义置信度\n"
"int32[] class_distribution           # 各类别的观测次数\n"
"float32[] class_probabilities        # 各类别的概率分布\n"
"\n"
"# 颜色信息\n"
"uint8 r                             # 红色分量 [0, 255]\n"
"uint8 g                             # 绿色分量 [0, 255] \n"
"uint8 b                             # 蓝色分量 [0, 255]\n"
"\n"
"# 特殊属性\n"
"bool is_fire                        # 是否包含火焰\n"
"bool is_obstacle                    # 是否为障碍物\n"
"bool is_navigable                   # 是否可通行\n"
"bool is_dynamic                     # 是否为动态物体\n"
"\n"
"# 更新信息\n"
"uint32 last_update_time             # 最后更新时间戳\n"
"uint32 observation_count            # 观测次数\n"
;
  }

  static const char* value(const ::semantic_mapping::SemanticVoxel_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.x);
      stream.next(m.y);
      stream.next(m.z);
      stream.next(m.occupancy_probability);
      stream.next(m.tsdf_value);
      stream.next(m.weight);
      stream.next(m.semantic_class);
      stream.next(m.semantic_confidence);
      stream.next(m.class_distribution);
      stream.next(m.class_probabilities);
      stream.next(m.r);
      stream.next(m.g);
      stream.next(m.b);
      stream.next(m.is_fire);
      stream.next(m.is_obstacle);
      stream.next(m.is_navigable);
      stream.next(m.is_dynamic);
      stream.next(m.last_update_time);
      stream.next(m.observation_count);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SemanticVoxel_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_mapping::SemanticVoxel_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "x: ";
    Printer<int32_t>::stream(s, indent + "  ", v.x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "y: ";
    Printer<int32_t>::stream(s, indent + "  ", v.y);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "z: ";
    Printer<int32_t>::stream(s, indent + "  ", v.z);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "occupancy_probability: ";
    Printer<float>::stream(s, indent + "  ", v.occupancy_probability);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "tsdf_value: ";
    Printer<float>::stream(s, indent + "  ", v.tsdf_value);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "weight: ";
    Printer<float>::stream(s, indent + "  ", v.weight);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "semantic_class: ";
    Printer<int32_t>::stream(s, indent + "  ", v.semantic_class);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "semantic_confidence: ";
    Printer<float>::stream(s, indent + "  ", v.semantic_confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_distribution: ";
    if (v.class_distribution.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_distribution.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.class_distribution[i]);
    }
    if (v.class_distribution.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_probabilities: ";
    if (v.class_probabilities.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_probabilities.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.class_probabilities[i]);
    }
    if (v.class_probabilities.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "r: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.r);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "g: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.g);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "b: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.b);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_fire: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_fire);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_obstacle: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_obstacle);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_navigable: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_navigable);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_dynamic: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_dynamic);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "last_update_time: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.last_update_time);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "observation_count: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.observation_count);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_SEMANTICVOXEL_H
