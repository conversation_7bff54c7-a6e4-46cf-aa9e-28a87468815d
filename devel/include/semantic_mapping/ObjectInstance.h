// Generated by gencpp from file semantic_mapping/ObjectInstance.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_OBJECTINSTANCE_H
#define SEMANTIC_MAPPING_MESSAGE_OBJECTINSTANCE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/Point.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Quaternion.h>
#include <semantic_mapping/SemanticVoxel.h>

namespace semantic_mapping
{
template <class ContainerAllocator>
struct ObjectInstance_
{
  typedef ObjectInstance_<ContainerAllocator> Type;

  ObjectInstance_()
    : header()
    , instance_id()
    , class_name()
    , class_id(0)
    , confidence(0.0)
    , centroid()
    , bounding_box()
    , orientation()
    , voxels()
    , voxel_count(0)
    , volume(0.0)
    , is_complete(false)
    , is_static(false)
    , is_navigable(false)
    , is_obstacle(false)
    , is_fire(false)
    , first_observed(0)
    , last_updated(0)
    , observation_count(0)  {
    }
  ObjectInstance_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , instance_id(_alloc)
    , class_name(_alloc)
    , class_id(0)
    , confidence(0.0)
    , centroid(_alloc)
    , bounding_box(_alloc)
    , orientation(_alloc)
    , voxels(_alloc)
    , voxel_count(0)
    , volume(0.0)
    , is_complete(false)
    , is_static(false)
    , is_navigable(false)
    , is_obstacle(false)
    , is_fire(false)
    , first_observed(0)
    , last_updated(0)
    , observation_count(0)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _instance_id_type;
  _instance_id_type instance_id;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _class_name_type;
  _class_name_type class_name;

   typedef int32_t _class_id_type;
  _class_id_type class_id;

   typedef float _confidence_type;
  _confidence_type confidence;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _centroid_type;
  _centroid_type centroid;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _bounding_box_type;
  _bounding_box_type bounding_box;

   typedef  ::geometry_msgs::Quaternion_<ContainerAllocator>  _orientation_type;
  _orientation_type orientation;

   typedef std::vector< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >> _voxels_type;
  _voxels_type voxels;

   typedef int32_t _voxel_count_type;
  _voxel_count_type voxel_count;

   typedef float _volume_type;
  _volume_type volume;

   typedef uint8_t _is_complete_type;
  _is_complete_type is_complete;

   typedef uint8_t _is_static_type;
  _is_static_type is_static;

   typedef uint8_t _is_navigable_type;
  _is_navigable_type is_navigable;

   typedef uint8_t _is_obstacle_type;
  _is_obstacle_type is_obstacle;

   typedef uint8_t _is_fire_type;
  _is_fire_type is_fire;

   typedef uint32_t _first_observed_type;
  _first_observed_type first_observed;

   typedef uint32_t _last_updated_type;
  _last_updated_type last_updated;

   typedef int32_t _observation_count_type;
  _observation_count_type observation_count;





  typedef boost::shared_ptr< ::semantic_mapping::ObjectInstance_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_mapping::ObjectInstance_<ContainerAllocator> const> ConstPtr;

}; // struct ObjectInstance_

typedef ::semantic_mapping::ObjectInstance_<std::allocator<void> > ObjectInstance;

typedef boost::shared_ptr< ::semantic_mapping::ObjectInstance > ObjectInstancePtr;
typedef boost::shared_ptr< ::semantic_mapping::ObjectInstance const> ObjectInstanceConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_mapping::ObjectInstance_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_mapping::ObjectInstance_<ContainerAllocator1> & lhs, const ::semantic_mapping::ObjectInstance_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.instance_id == rhs.instance_id &&
    lhs.class_name == rhs.class_name &&
    lhs.class_id == rhs.class_id &&
    lhs.confidence == rhs.confidence &&
    lhs.centroid == rhs.centroid &&
    lhs.bounding_box == rhs.bounding_box &&
    lhs.orientation == rhs.orientation &&
    lhs.voxels == rhs.voxels &&
    lhs.voxel_count == rhs.voxel_count &&
    lhs.volume == rhs.volume &&
    lhs.is_complete == rhs.is_complete &&
    lhs.is_static == rhs.is_static &&
    lhs.is_navigable == rhs.is_navigable &&
    lhs.is_obstacle == rhs.is_obstacle &&
    lhs.is_fire == rhs.is_fire &&
    lhs.first_observed == rhs.first_observed &&
    lhs.last_updated == rhs.last_updated &&
    lhs.observation_count == rhs.observation_count;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_mapping::ObjectInstance_<ContainerAllocator1> & lhs, const ::semantic_mapping::ObjectInstance_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_mapping

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::ObjectInstance_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::ObjectInstance_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::ObjectInstance_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >
{
  static const char* value()
  {
    return "15c32ccab0b7c6804a0d943b929a16a6";
  }

  static const char* value(const ::semantic_mapping::ObjectInstance_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x15c32ccab0b7c680ULL;
  static const uint64_t static_value2 = 0x4a0d943b929a16a6ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_mapping/ObjectInstance";
  }

  static const char* value(const ::semantic_mapping::ObjectInstance_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 物体实例消息\n"
"Header header\n"
"\n"
"# 实例基本信息\n"
"string instance_id                   # 实例唯一标识符\n"
"string class_name                    # 物体类别名称\n"
"int32 class_id                       # 物体类别ID\n"
"float32 confidence                   # 实例置信度 [0.0, 1.0]\n"
"\n"
"# 几何信息\n"
"geometry_msgs/Point centroid         # 质心位置\n"
"geometry_msgs/Vector3 bounding_box   # 边界框尺寸\n"
"geometry_msgs/Quaternion orientation # 方向\n"
"\n"
"# 体素信息\n"
"SemanticVoxel[] voxels              # 组成实例的体素列表\n"
"int32 voxel_count                   # 体素数量\n"
"float32 volume                      # 体积 (立方米)\n"
"\n"
"# 语义属性\n"
"bool is_complete                    # 是否完整观测\n"
"bool is_static                      # 是否静态\n"
"bool is_navigable                   # 是否可通行\n"
"bool is_obstacle                    # 是否为障碍物\n"
"bool is_fire                        # 是否为火焰\n"
"\n"
"# 时间信息\n"
"uint32 first_observed              # 首次观测时间\n"
"uint32 last_updated                # 最后更新时间\n"
"int32 observation_count            # 观测次数\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
"\n"
"================================================================================\n"
"MSG: semantic_mapping/SemanticVoxel\n"
"# 语义体素消息\n"
"# 体素位置 (网格坐标)\n"
"int32 x\n"
"int32 y  \n"
"int32 z\n"
"\n"
"# 几何信息\n"
"float32 occupancy_probability        # 占用概率 [0.0, 1.0]\n"
"float32 tsdf_value                   # TSDF值\n"
"float32 weight                       # 融合权重\n"
"\n"
"# 语义信息\n"
"int32 semantic_class                 # 主要语义类别ID\n"
"float32 semantic_confidence          # 语义置信度\n"
"int32[] class_distribution           # 各类别的观测次数\n"
"float32[] class_probabilities        # 各类别的概率分布\n"
"\n"
"# 颜色信息\n"
"uint8 r                             # 红色分量 [0, 255]\n"
"uint8 g                             # 绿色分量 [0, 255] \n"
"uint8 b                             # 蓝色分量 [0, 255]\n"
"\n"
"# 特殊属性\n"
"bool is_fire                        # 是否包含火焰\n"
"bool is_obstacle                    # 是否为障碍物\n"
"bool is_navigable                   # 是否可通行\n"
"bool is_dynamic                     # 是否为动态物体\n"
"\n"
"# 更新信息\n"
"uint32 last_update_time             # 最后更新时间戳\n"
"uint32 observation_count            # 观测次数\n"
;
  }

  static const char* value(const ::semantic_mapping::ObjectInstance_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.instance_id);
      stream.next(m.class_name);
      stream.next(m.class_id);
      stream.next(m.confidence);
      stream.next(m.centroid);
      stream.next(m.bounding_box);
      stream.next(m.orientation);
      stream.next(m.voxels);
      stream.next(m.voxel_count);
      stream.next(m.volume);
      stream.next(m.is_complete);
      stream.next(m.is_static);
      stream.next(m.is_navigable);
      stream.next(m.is_obstacle);
      stream.next(m.is_fire);
      stream.next(m.first_observed);
      stream.next(m.last_updated);
      stream.next(m.observation_count);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct ObjectInstance_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_mapping::ObjectInstance_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "instance_id: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.instance_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.class_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.class_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<float>::stream(s, indent + "  ", v.confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "centroid: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.centroid);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "bounding_box: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.bounding_box);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "orientation: ";
    Printer< ::geometry_msgs::Quaternion_<ContainerAllocator> >::stream(s, indent + "  ", v.orientation);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "voxels: ";
    if (v.voxels.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.voxels.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.voxels[i]);
    }
    if (v.voxels.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "voxel_count: ";
    Printer<int32_t>::stream(s, indent + "  ", v.voxel_count);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "volume: ";
    Printer<float>::stream(s, indent + "  ", v.volume);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_complete: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_complete);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_static: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_static);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_navigable: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_navigable);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_obstacle: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_obstacle);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_fire: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_fire);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "first_observed: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.first_observed);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "last_updated: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.last_updated);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "observation_count: ";
    Printer<int32_t>::stream(s, indent + "  ", v.observation_count);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_OBJECTINSTANCE_H
