// Generated by gencpp from file semantic_mapping/SemanticMap.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_SEMANTICMAP_H
#define SEMANTIC_MAPPING_MESSAGE_SEMANTICMAP_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/Pose.h>
#include <geometry_msgs/Vector3.h>
#include <semantic_mapping/SemanticVoxel.h>
#include <geometry_msgs/Point.h>
#include <geometry_msgs/Point.h>
#include <geometry_msgs/Polygon.h>

namespace semantic_mapping
{
template <class ContainerAllocator>
struct SemanticMap_
{
  typedef SemanticMap_<ContainerAllocator> Type;

  SemanticMap_()
    : header()
    , frame_id()
    , origin()
    , resolution(0.0)
    , size()
    , dimensions()
    , voxels()
    , class_names()
    , class_ids()
    , class_colors()
    , total_voxels(0)
    , occupied_voxels(0)
    , semantic_voxels(0)
    , map_confidence(0.0)
    , fire_locations()
    , obstacle_centers()
    , navigable_areas()
    , last_update_time(0)
    , version(0)  {
    }
  SemanticMap_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , frame_id(_alloc)
    , origin(_alloc)
    , resolution(0.0)
    , size(_alloc)
    , dimensions(_alloc)
    , voxels(_alloc)
    , class_names(_alloc)
    , class_ids(_alloc)
    , class_colors(_alloc)
    , total_voxels(0)
    , occupied_voxels(0)
    , semantic_voxels(0)
    , map_confidence(0.0)
    , fire_locations(_alloc)
    , obstacle_centers(_alloc)
    , navigable_areas(_alloc)
    , last_update_time(0)
    , version(0)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _frame_id_type;
  _frame_id_type frame_id;

   typedef  ::geometry_msgs::Pose_<ContainerAllocator>  _origin_type;
  _origin_type origin;

   typedef float _resolution_type;
  _resolution_type resolution;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _size_type;
  _size_type size;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _dimensions_type;
  _dimensions_type dimensions;

   typedef std::vector< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >> _voxels_type;
  _voxels_type voxels;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _class_names_type;
  _class_names_type class_names;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _class_ids_type;
  _class_ids_type class_ids;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _class_colors_type;
  _class_colors_type class_colors;

   typedef int32_t _total_voxels_type;
  _total_voxels_type total_voxels;

   typedef int32_t _occupied_voxels_type;
  _occupied_voxels_type occupied_voxels;

   typedef int32_t _semantic_voxels_type;
  _semantic_voxels_type semantic_voxels;

   typedef float _map_confidence_type;
  _map_confidence_type map_confidence;

   typedef std::vector< ::geometry_msgs::Point_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Point_<ContainerAllocator> >> _fire_locations_type;
  _fire_locations_type fire_locations;

   typedef std::vector< ::geometry_msgs::Point_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Point_<ContainerAllocator> >> _obstacle_centers_type;
  _obstacle_centers_type obstacle_centers;

   typedef std::vector< ::geometry_msgs::Polygon_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Polygon_<ContainerAllocator> >> _navigable_areas_type;
  _navigable_areas_type navigable_areas;

   typedef uint32_t _last_update_time_type;
  _last_update_time_type last_update_time;

   typedef uint32_t _version_type;
  _version_type version;





  typedef boost::shared_ptr< ::semantic_mapping::SemanticMap_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_mapping::SemanticMap_<ContainerAllocator> const> ConstPtr;

}; // struct SemanticMap_

typedef ::semantic_mapping::SemanticMap_<std::allocator<void> > SemanticMap;

typedef boost::shared_ptr< ::semantic_mapping::SemanticMap > SemanticMapPtr;
typedef boost::shared_ptr< ::semantic_mapping::SemanticMap const> SemanticMapConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_mapping::SemanticMap_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_mapping::SemanticMap_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_mapping::SemanticMap_<ContainerAllocator1> & lhs, const ::semantic_mapping::SemanticMap_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.frame_id == rhs.frame_id &&
    lhs.origin == rhs.origin &&
    lhs.resolution == rhs.resolution &&
    lhs.size == rhs.size &&
    lhs.dimensions == rhs.dimensions &&
    lhs.voxels == rhs.voxels &&
    lhs.class_names == rhs.class_names &&
    lhs.class_ids == rhs.class_ids &&
    lhs.class_colors == rhs.class_colors &&
    lhs.total_voxels == rhs.total_voxels &&
    lhs.occupied_voxels == rhs.occupied_voxels &&
    lhs.semantic_voxels == rhs.semantic_voxels &&
    lhs.map_confidence == rhs.map_confidence &&
    lhs.fire_locations == rhs.fire_locations &&
    lhs.obstacle_centers == rhs.obstacle_centers &&
    lhs.navigable_areas == rhs.navigable_areas &&
    lhs.last_update_time == rhs.last_update_time &&
    lhs.version == rhs.version;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_mapping::SemanticMap_<ContainerAllocator1> & lhs, const ::semantic_mapping::SemanticMap_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_mapping

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::SemanticMap_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::SemanticMap_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::SemanticMap_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::SemanticMap_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::SemanticMap_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::SemanticMap_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_mapping::SemanticMap_<ContainerAllocator> >
{
  static const char* value()
  {
    return "2a903e81ceee85d5bcb8e5aa6d9cd5e7";
  }

  static const char* value(const ::semantic_mapping::SemanticMap_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x2a903e81ceee85d5ULL;
  static const uint64_t static_value2 = 0xbcb8e5aa6d9cd5e7ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_mapping::SemanticMap_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_mapping/SemanticMap";
  }

  static const char* value(const ::semantic_mapping::SemanticMap_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_mapping::SemanticMap_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 语义地图消息\n"
"Header header\n"
"\n"
"# 地图元信息\n"
"string frame_id                      # 坐标系ID\n"
"geometry_msgs/Pose origin            # 地图原点位置\n"
"float32 resolution                   # 体素分辨率 (米)\n"
"geometry_msgs/Vector3 size           # 地图尺寸 (米)\n"
"int32[] dimensions                   # 网格维度 [x, y, z]\n"
"\n"
"# 体素数据\n"
"SemanticVoxel[] voxels              # 语义体素数组\n"
"\n"
"# 类别信息\n"
"string[] class_names                 # 类别名称列表\n"
"int32[] class_ids                    # 类别ID列表\n"
"int32[] class_colors                 # 类别颜色 (RGB)\n"
"\n"
"# 统计信息\n"
"int32 total_voxels                   # 总体素数量\n"
"int32 occupied_voxels                # 占用体素数量\n"
"int32 semantic_voxels                # 有语义标注的体素数量\n"
"float32 map_confidence               # 整体地图置信度\n"
"\n"
"# 特殊区域\n"
"geometry_msgs/Point[] fire_locations # 火焰位置列表\n"
"geometry_msgs/Point[] obstacle_centers # 障碍物中心列表\n"
"geometry_msgs/Polygon[] navigable_areas # 可通行区域\n"
"\n"
"# 更新信息\n"
"uint32 last_update_time             # 最后更新时间\n"
"uint32 version                      # 地图版本号\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"================================================================================\n"
"MSG: semantic_mapping/SemanticVoxel\n"
"# 语义体素消息\n"
"# 体素位置 (网格坐标)\n"
"int32 x\n"
"int32 y  \n"
"int32 z\n"
"\n"
"# 几何信息\n"
"float32 occupancy_probability        # 占用概率 [0.0, 1.0]\n"
"float32 tsdf_value                   # TSDF值\n"
"float32 weight                       # 融合权重\n"
"\n"
"# 语义信息\n"
"int32 semantic_class                 # 主要语义类别ID\n"
"float32 semantic_confidence          # 语义置信度\n"
"int32[] class_distribution           # 各类别的观测次数\n"
"float32[] class_probabilities        # 各类别的概率分布\n"
"\n"
"# 颜色信息\n"
"uint8 r                             # 红色分量 [0, 255]\n"
"uint8 g                             # 绿色分量 [0, 255] \n"
"uint8 b                             # 蓝色分量 [0, 255]\n"
"\n"
"# 特殊属性\n"
"bool is_fire                        # 是否包含火焰\n"
"bool is_obstacle                    # 是否为障碍物\n"
"bool is_navigable                   # 是否可通行\n"
"bool is_dynamic                     # 是否为动态物体\n"
"\n"
"# 更新信息\n"
"uint32 last_update_time             # 最后更新时间戳\n"
"uint32 observation_count            # 观测次数\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Polygon\n"
"#A specification of a polygon where the first and last points are assumed to be connected\n"
"Point32[] points\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point32\n"
"# This contains the position of a point in free space(with 32 bits of precision).\n"
"# It is recommeded to use Point wherever possible instead of Point32.  \n"
"# \n"
"# This recommendation is to promote interoperability.  \n"
"#\n"
"# This message is designed to take up less space when sending\n"
"# lots of points at once, as in the case of a PointCloud.  \n"
"\n"
"float32 x\n"
"float32 y\n"
"float32 z\n"
;
  }

  static const char* value(const ::semantic_mapping::SemanticMap_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_mapping::SemanticMap_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.frame_id);
      stream.next(m.origin);
      stream.next(m.resolution);
      stream.next(m.size);
      stream.next(m.dimensions);
      stream.next(m.voxels);
      stream.next(m.class_names);
      stream.next(m.class_ids);
      stream.next(m.class_colors);
      stream.next(m.total_voxels);
      stream.next(m.occupied_voxels);
      stream.next(m.semantic_voxels);
      stream.next(m.map_confidence);
      stream.next(m.fire_locations);
      stream.next(m.obstacle_centers);
      stream.next(m.navigable_areas);
      stream.next(m.last_update_time);
      stream.next(m.version);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SemanticMap_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_mapping::SemanticMap_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_mapping::SemanticMap_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "frame_id: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.frame_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "origin: ";
    Printer< ::geometry_msgs::Pose_<ContainerAllocator> >::stream(s, indent + "  ", v.origin);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "resolution: ";
    Printer<float>::stream(s, indent + "  ", v.resolution);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "size: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.size);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "dimensions: ";
    if (v.dimensions.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.dimensions.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.dimensions[i]);
    }
    if (v.dimensions.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "voxels: ";
    if (v.voxels.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.voxels.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::semantic_mapping::SemanticVoxel_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.voxels[i]);
    }
    if (v.voxels.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_names: ";
    if (v.class_names.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_names.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.class_names[i]);
    }
    if (v.class_names.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_ids: ";
    if (v.class_ids.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_ids.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.class_ids[i]);
    }
    if (v.class_ids.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_colors: ";
    if (v.class_colors.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.class_colors.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.class_colors[i]);
    }
    if (v.class_colors.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "total_voxels: ";
    Printer<int32_t>::stream(s, indent + "  ", v.total_voxels);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "occupied_voxels: ";
    Printer<int32_t>::stream(s, indent + "  ", v.occupied_voxels);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "semantic_voxels: ";
    Printer<int32_t>::stream(s, indent + "  ", v.semantic_voxels);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "map_confidence: ";
    Printer<float>::stream(s, indent + "  ", v.map_confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "fire_locations: ";
    if (v.fire_locations.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.fire_locations.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.fire_locations[i]);
    }
    if (v.fire_locations.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "obstacle_centers: ";
    if (v.obstacle_centers.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.obstacle_centers.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.obstacle_centers[i]);
    }
    if (v.obstacle_centers.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "navigable_areas: ";
    if (v.navigable_areas.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.navigable_areas.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Polygon_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.navigable_areas[i]);
    }
    if (v.navigable_areas.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "last_update_time: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.last_update_time);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "version: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.version);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_SEMANTICMAP_H
