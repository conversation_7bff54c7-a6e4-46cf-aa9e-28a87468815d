// Generated by gencpp from file semantic_mapping/UpdateSemanticMapRequest.msg
// DO NOT EDIT!


#ifndef SEMANTIC_MAPPING_MESSAGE_UPDATESEMANTICMAPREQUEST_H
#define SEMANTIC_MAPPING_MESSAGE_UPDATESEMANTICMAPREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <semantic_mapping/ObjectInstance.h>
#include <geometry_msgs/Point.h>

namespace semantic_mapping
{
template <class ContainerAllocator>
struct UpdateSemanticMapRequest_
{
  typedef UpdateSemanticMapRequest_<ContainerAllocator> Type;

  UpdateSemanticMapRequest_()
    : update_type()
    , objects()
    , fire_locations()
    , clear_old_data(false)  {
    }
  UpdateSemanticMapRequest_(const ContainerAllocator& _alloc)
    : update_type(_alloc)
    , objects(_alloc)
    , fire_locations(_alloc)
    , clear_old_data(false)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _update_type_type;
  _update_type_type update_type;

   typedef std::vector< ::semantic_mapping::ObjectInstance_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >> _objects_type;
  _objects_type objects;

   typedef std::vector< ::geometry_msgs::Point_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Point_<ContainerAllocator> >> _fire_locations_type;
  _fire_locations_type fire_locations;

   typedef uint8_t _clear_old_data_type;
  _clear_old_data_type clear_old_data;





  typedef boost::shared_ptr< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> const> ConstPtr;

}; // struct UpdateSemanticMapRequest_

typedef ::semantic_mapping::UpdateSemanticMapRequest_<std::allocator<void> > UpdateSemanticMapRequest;

typedef boost::shared_ptr< ::semantic_mapping::UpdateSemanticMapRequest > UpdateSemanticMapRequestPtr;
typedef boost::shared_ptr< ::semantic_mapping::UpdateSemanticMapRequest const> UpdateSemanticMapRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator1> & lhs, const ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator2> & rhs)
{
  return lhs.update_type == rhs.update_type &&
    lhs.objects == rhs.objects &&
    lhs.fire_locations == rhs.fire_locations &&
    lhs.clear_old_data == rhs.clear_old_data;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator1> & lhs, const ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_mapping

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "8d4781900717c4916dda0476c5303f9e";
  }

  static const char* value(const ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x8d4781900717c491ULL;
  static const uint64_t static_value2 = 0x6dda0476c5303f9eULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_mapping/UpdateSemanticMapRequest";
  }

  static const char* value(const ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 更新语义地图服务\n"
"# 请求\n"
"string update_type                  # 更新类型 (add, remove, modify)\n"
"ObjectInstance[] objects            # 要更新的物体实例\n"
"geometry_msgs/Point[] fire_locations # 火焰位置\n"
"bool clear_old_data                 # 是否清除旧数据\n"
"\n"
"\n"
"================================================================================\n"
"MSG: semantic_mapping/ObjectInstance\n"
"# 物体实例消息\n"
"Header header\n"
"\n"
"# 实例基本信息\n"
"string instance_id                   # 实例唯一标识符\n"
"string class_name                    # 物体类别名称\n"
"int32 class_id                       # 物体类别ID\n"
"float32 confidence                   # 实例置信度 [0.0, 1.0]\n"
"\n"
"# 几何信息\n"
"geometry_msgs/Point centroid         # 质心位置\n"
"geometry_msgs/Vector3 bounding_box   # 边界框尺寸\n"
"geometry_msgs/Quaternion orientation # 方向\n"
"\n"
"# 体素信息\n"
"SemanticVoxel[] voxels              # 组成实例的体素列表\n"
"int32 voxel_count                   # 体素数量\n"
"float32 volume                      # 体积 (立方米)\n"
"\n"
"# 语义属性\n"
"bool is_complete                    # 是否完整观测\n"
"bool is_static                      # 是否静态\n"
"bool is_navigable                   # 是否可通行\n"
"bool is_obstacle                    # 是否为障碍物\n"
"bool is_fire                        # 是否为火焰\n"
"\n"
"# 时间信息\n"
"uint32 first_observed              # 首次观测时间\n"
"uint32 last_updated                # 最后更新时间\n"
"int32 observation_count            # 观测次数\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
"\n"
"================================================================================\n"
"MSG: semantic_mapping/SemanticVoxel\n"
"# 语义体素消息\n"
"# 体素位置 (网格坐标)\n"
"int32 x\n"
"int32 y  \n"
"int32 z\n"
"\n"
"# 几何信息\n"
"float32 occupancy_probability        # 占用概率 [0.0, 1.0]\n"
"float32 tsdf_value                   # TSDF值\n"
"float32 weight                       # 融合权重\n"
"\n"
"# 语义信息\n"
"int32 semantic_class                 # 主要语义类别ID\n"
"float32 semantic_confidence          # 语义置信度\n"
"int32[] class_distribution           # 各类别的观测次数\n"
"float32[] class_probabilities        # 各类别的概率分布\n"
"\n"
"# 颜色信息\n"
"uint8 r                             # 红色分量 [0, 255]\n"
"uint8 g                             # 绿色分量 [0, 255] \n"
"uint8 b                             # 蓝色分量 [0, 255]\n"
"\n"
"# 特殊属性\n"
"bool is_fire                        # 是否包含火焰\n"
"bool is_obstacle                    # 是否为障碍物\n"
"bool is_navigable                   # 是否可通行\n"
"bool is_dynamic                     # 是否为动态物体\n"
"\n"
"# 更新信息\n"
"uint32 last_update_time             # 最后更新时间戳\n"
"uint32 observation_count            # 观测次数\n"
;
  }

  static const char* value(const ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.update_type);
      stream.next(m.objects);
      stream.next(m.fire_locations);
      stream.next(m.clear_old_data);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct UpdateSemanticMapRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_mapping::UpdateSemanticMapRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "update_type: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.update_type);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "objects: ";
    if (v.objects.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.objects.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::semantic_mapping::ObjectInstance_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.objects[i]);
    }
    if (v.objects.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "fire_locations: ";
    if (v.fire_locations.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.fire_locations.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.fire_locations[i]);
    }
    if (v.fire_locations.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "clear_old_data: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.clear_old_data);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_MAPPING_MESSAGE_UPDATESEMANTICMAPREQUEST_H
