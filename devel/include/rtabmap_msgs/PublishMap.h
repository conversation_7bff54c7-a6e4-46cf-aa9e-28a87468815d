// Generated by gencpp from file rtabmap_msgs/PublishMap.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_PUBLISHMAP_H
#define RTABMAP_MSGS_MESSAGE_PUBLISHMAP_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/PublishMapRequest.h>
#include <rtabmap_msgs/PublishMapResponse.h>


namespace rtabmap_msgs
{

struct PublishMap
{

typedef PublishMapRequest Request;
typedef PublishMapResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct PublishMap
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::PublishMap > {
  static const char* value()
  {
    return "6213f9f13cced23f4d224b22f59d839c";
  }

  static const char* value(const ::rtabmap_msgs::PublishMap&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::PublishMap > {
  static const char* value()
  {
    return "rtabmap_msgs/PublishMap";
  }

  static const char* value(const ::rtabmap_msgs::PublishMap&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::PublishMapRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::PublishMap >
template<>
struct MD5Sum< ::rtabmap_msgs::PublishMapRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::PublishMap >::value();
  }
  static const char* value(const ::rtabmap_msgs::PublishMapRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::PublishMapRequest> should match
// service_traits::DataType< ::rtabmap_msgs::PublishMap >
template<>
struct DataType< ::rtabmap_msgs::PublishMapRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::PublishMap >::value();
  }
  static const char* value(const ::rtabmap_msgs::PublishMapRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::PublishMapResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::PublishMap >
template<>
struct MD5Sum< ::rtabmap_msgs::PublishMapResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::PublishMap >::value();
  }
  static const char* value(const ::rtabmap_msgs::PublishMapResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::PublishMapResponse> should match
// service_traits::DataType< ::rtabmap_msgs::PublishMap >
template<>
struct DataType< ::rtabmap_msgs::PublishMapResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::PublishMap >::value();
  }
  static const char* value(const ::rtabmap_msgs::PublishMapResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_PUBLISHMAP_H
