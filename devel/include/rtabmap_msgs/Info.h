// Generated by gencpp from file rtabmap_msgs/Info.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_INFO_H
#define RTABMAP_MSGS_MESSAGE_INFO_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/Transform.h>
#include <rtabmap_msgs/MapGraph.h>

namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct Info_
{
  typedef Info_<ContainerAllocator> Type;

  Info_()
    : header()
    , refId(0)
    , loopClosureId(0)
    , proximityDetectionId(0)
    , landmarkId(0)
    , loopClosureTransform()
    , wmState()
    , posteriorKeys()
    , posteriorValues()
    , likelihoodKeys()
    , likelihoodValues()
    , rawLikelihoodKeys()
    , rawLikelihoodValues()
    , weightsKeys()
    , weightsValues()
    , labelsKeys()
    , labelsValues()
    , statsKeys()
    , statsValues()
    , localPath()
    , currentGoalId(0)
    , odom_cache()  {
    }
  Info_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , refId(0)
    , loopClosureId(0)
    , proximityDetectionId(0)
    , landmarkId(0)
    , loopClosureTransform(_alloc)
    , wmState(_alloc)
    , posteriorKeys(_alloc)
    , posteriorValues(_alloc)
    , likelihoodKeys(_alloc)
    , likelihoodValues(_alloc)
    , rawLikelihoodKeys(_alloc)
    , rawLikelihoodValues(_alloc)
    , weightsKeys(_alloc)
    , weightsValues(_alloc)
    , labelsKeys(_alloc)
    , labelsValues(_alloc)
    , statsKeys(_alloc)
    , statsValues(_alloc)
    , localPath(_alloc)
    , currentGoalId(0)
    , odom_cache(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef int32_t _refId_type;
  _refId_type refId;

   typedef int32_t _loopClosureId_type;
  _loopClosureId_type loopClosureId;

   typedef int32_t _proximityDetectionId_type;
  _proximityDetectionId_type proximityDetectionId;

   typedef int32_t _landmarkId_type;
  _landmarkId_type landmarkId;

   typedef  ::geometry_msgs::Transform_<ContainerAllocator>  _loopClosureTransform_type;
  _loopClosureTransform_type loopClosureTransform;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _wmState_type;
  _wmState_type wmState;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _posteriorKeys_type;
  _posteriorKeys_type posteriorKeys;

   typedef std::vector<float, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<float>> _posteriorValues_type;
  _posteriorValues_type posteriorValues;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _likelihoodKeys_type;
  _likelihoodKeys_type likelihoodKeys;

   typedef std::vector<float, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<float>> _likelihoodValues_type;
  _likelihoodValues_type likelihoodValues;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _rawLikelihoodKeys_type;
  _rawLikelihoodKeys_type rawLikelihoodKeys;

   typedef std::vector<float, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<float>> _rawLikelihoodValues_type;
  _rawLikelihoodValues_type rawLikelihoodValues;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _weightsKeys_type;
  _weightsKeys_type weightsKeys;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _weightsValues_type;
  _weightsValues_type weightsValues;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _labelsKeys_type;
  _labelsKeys_type labelsKeys;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _labelsValues_type;
  _labelsValues_type labelsValues;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _statsKeys_type;
  _statsKeys_type statsKeys;

   typedef std::vector<float, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<float>> _statsValues_type;
  _statsValues_type statsValues;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _localPath_type;
  _localPath_type localPath;

   typedef int32_t _currentGoalId_type;
  _currentGoalId_type currentGoalId;

   typedef  ::rtabmap_msgs::MapGraph_<ContainerAllocator>  _odom_cache_type;
  _odom_cache_type odom_cache;





  typedef boost::shared_ptr< ::rtabmap_msgs::Info_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::Info_<ContainerAllocator> const> ConstPtr;

}; // struct Info_

typedef ::rtabmap_msgs::Info_<std::allocator<void> > Info;

typedef boost::shared_ptr< ::rtabmap_msgs::Info > InfoPtr;
typedef boost::shared_ptr< ::rtabmap_msgs::Info const> InfoConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::Info_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::Info_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::Info_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::Info_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.refId == rhs.refId &&
    lhs.loopClosureId == rhs.loopClosureId &&
    lhs.proximityDetectionId == rhs.proximityDetectionId &&
    lhs.landmarkId == rhs.landmarkId &&
    lhs.loopClosureTransform == rhs.loopClosureTransform &&
    lhs.wmState == rhs.wmState &&
    lhs.posteriorKeys == rhs.posteriorKeys &&
    lhs.posteriorValues == rhs.posteriorValues &&
    lhs.likelihoodKeys == rhs.likelihoodKeys &&
    lhs.likelihoodValues == rhs.likelihoodValues &&
    lhs.rawLikelihoodKeys == rhs.rawLikelihoodKeys &&
    lhs.rawLikelihoodValues == rhs.rawLikelihoodValues &&
    lhs.weightsKeys == rhs.weightsKeys &&
    lhs.weightsValues == rhs.weightsValues &&
    lhs.labelsKeys == rhs.labelsKeys &&
    lhs.labelsValues == rhs.labelsValues &&
    lhs.statsKeys == rhs.statsKeys &&
    lhs.statsValues == rhs.statsValues &&
    lhs.localPath == rhs.localPath &&
    lhs.currentGoalId == rhs.currentGoalId &&
    lhs.odom_cache == rhs.odom_cache;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::Info_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::Info_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::Info_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::Info_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::Info_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::Info_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::Info_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::Info_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::Info_<ContainerAllocator> >
{
  static const char* value()
  {
    return "656fb5b8dfd8dfd2dc40226f35f2d440";
  }

  static const char* value(const ::rtabmap_msgs::Info_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x656fb5b8dfd8dfd2ULL;
  static const uint64_t static_value2 = 0xdc40226f35f2d440ULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::Info_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/Info";
  }

  static const char* value(const ::rtabmap_msgs::Info_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::Info_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"########################################\n"
"# RTAB-Map info with statistics \n"
"########################################\n"
"\n"
"Header header\n"
"\n"
"int32 refId\n"
"int32 loopClosureId\n"
"int32 proximityDetectionId\n"
"int32 landmarkId\n"
"\n"
"geometry_msgs/Transform loopClosureTransform\n"
"\n"
"####\n"
"# For statistics...\n"
"####\n"
"# State (node IDs) of the current Working Memory (including STM)\n"
"int32[] wmState\n"
"\n"
"# std::map<int, float> posterior;\n"
"int32[] posteriorKeys\n"
"float32[] posteriorValues\n"
"\n"
"# std::map<int, float> likelihood;\n"
"int32[] likelihoodKeys\n"
"float32[] likelihoodValues\n"
"\n"
"# std::map<int, float> rawLikelihood;\n"
"int32[] rawLikelihoodKeys\n"
"float32[] rawLikelihoodValues\n"
"\n"
"# std::map<int, int> weights;\n"
"int32[] weightsKeys\n"
"int32[] weightsValues\n"
"\n"
"# std::map<int, std::string> labels;\n"
"int32[] labelsKeys\n"
"string[] labelsValues\n"
"\n"
"# std::map<std::string, float> stats\n"
"string[] statsKeys\n"
"float32[] statsValues\n"
"\n"
"# std::vector<int> localPath\n"
"int32[] localPath\n"
"int32 currentGoalId\n"
"\n"
"# std::vector<int> odomCache\n"
"MapGraph odom_cache\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Transform\n"
"# This represents the transform between two coordinate frames in free space.\n"
"\n"
"Vector3 translation\n"
"Quaternion rotation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
"\n"
"================================================================================\n"
"MSG: rtabmap_msgs/MapGraph\n"
"\n"
"Header header\n"
"\n"
"##\n"
"# /map to /odom transform\n"
"# Always identity when the graph is optimized from the latest pose.\n"
"##\n"
"geometry_msgs/Transform mapToOdom\n"
"\n"
"# The poses\n"
"int32[] posesId\n"
"geometry_msgs/Pose[] poses\n"
"\n"
"# The links\n"
"Link[] links\n"
"\n"
"\n"
"\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: rtabmap_msgs/Link\n"
"#class rtabmap::Link\n"
"#{\n"
"#   int from;\n"
"#   int to;\n"
"#   Type type;\n"
"#   Transform transform;\n"
"#   cv::Mat(6,6,CV_64FC1) information;\n"
"#}\n"
"\n"
"int32 fromId\n"
"int32 toId\n"
"int32 type\n"
"geometry_msgs/Transform transform\n"
"float64[36] information\n"
;
  }

  static const char* value(const ::rtabmap_msgs::Info_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::Info_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.refId);
      stream.next(m.loopClosureId);
      stream.next(m.proximityDetectionId);
      stream.next(m.landmarkId);
      stream.next(m.loopClosureTransform);
      stream.next(m.wmState);
      stream.next(m.posteriorKeys);
      stream.next(m.posteriorValues);
      stream.next(m.likelihoodKeys);
      stream.next(m.likelihoodValues);
      stream.next(m.rawLikelihoodKeys);
      stream.next(m.rawLikelihoodValues);
      stream.next(m.weightsKeys);
      stream.next(m.weightsValues);
      stream.next(m.labelsKeys);
      stream.next(m.labelsValues);
      stream.next(m.statsKeys);
      stream.next(m.statsValues);
      stream.next(m.localPath);
      stream.next(m.currentGoalId);
      stream.next(m.odom_cache);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Info_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::Info_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::Info_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "refId: ";
    Printer<int32_t>::stream(s, indent + "  ", v.refId);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "loopClosureId: ";
    Printer<int32_t>::stream(s, indent + "  ", v.loopClosureId);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "proximityDetectionId: ";
    Printer<int32_t>::stream(s, indent + "  ", v.proximityDetectionId);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "landmarkId: ";
    Printer<int32_t>::stream(s, indent + "  ", v.landmarkId);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "loopClosureTransform: ";
    Printer< ::geometry_msgs::Transform_<ContainerAllocator> >::stream(s, indent + "  ", v.loopClosureTransform);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "wmState: ";
    if (v.wmState.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.wmState.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.wmState[i]);
    }
    if (v.wmState.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "posteriorKeys: ";
    if (v.posteriorKeys.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.posteriorKeys.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.posteriorKeys[i]);
    }
    if (v.posteriorKeys.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "posteriorValues: ";
    if (v.posteriorValues.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.posteriorValues.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.posteriorValues[i]);
    }
    if (v.posteriorValues.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "likelihoodKeys: ";
    if (v.likelihoodKeys.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.likelihoodKeys.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.likelihoodKeys[i]);
    }
    if (v.likelihoodKeys.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "likelihoodValues: ";
    if (v.likelihoodValues.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.likelihoodValues.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.likelihoodValues[i]);
    }
    if (v.likelihoodValues.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "rawLikelihoodKeys: ";
    if (v.rawLikelihoodKeys.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.rawLikelihoodKeys.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.rawLikelihoodKeys[i]);
    }
    if (v.rawLikelihoodKeys.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "rawLikelihoodValues: ";
    if (v.rawLikelihoodValues.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.rawLikelihoodValues.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.rawLikelihoodValues[i]);
    }
    if (v.rawLikelihoodValues.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "weightsKeys: ";
    if (v.weightsKeys.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.weightsKeys.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.weightsKeys[i]);
    }
    if (v.weightsKeys.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "weightsValues: ";
    if (v.weightsValues.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.weightsValues.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.weightsValues[i]);
    }
    if (v.weightsValues.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "labelsKeys: ";
    if (v.labelsKeys.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.labelsKeys.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.labelsKeys[i]);
    }
    if (v.labelsKeys.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "labelsValues: ";
    if (v.labelsValues.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.labelsValues.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.labelsValues[i]);
    }
    if (v.labelsValues.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "statsKeys: ";
    if (v.statsKeys.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.statsKeys.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.statsKeys[i]);
    }
    if (v.statsKeys.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "statsValues: ";
    if (v.statsValues.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.statsValues.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.statsValues[i]);
    }
    if (v.statsValues.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "localPath: ";
    if (v.localPath.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.localPath.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.localPath[i]);
    }
    if (v.localPath.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "currentGoalId: ";
    Printer<int32_t>::stream(s, indent + "  ", v.currentGoalId);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "odom_cache: ";
    Printer< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >::stream(s, indent + "  ", v.odom_cache);
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_INFO_H
