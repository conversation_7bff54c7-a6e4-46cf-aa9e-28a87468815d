// Generated by gencpp from file rtabmap_msgs/GetMap.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GETMAP_H
#define RTABMAP_MSGS_MESSAGE_GETMAP_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/GetMapRequest.h>
#include <rtabmap_msgs/GetMapResponse.h>


namespace rtabmap_msgs
{

struct GetMap
{

typedef GetMapRequest Request;
typedef GetMapResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetMap
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::GetMap > {
  static const char* value()
  {
    return "2c1014e7caff3bb7b109fcfb04b11e52";
  }

  static const char* value(const ::rtabmap_msgs::GetMap&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::GetMap > {
  static const char* value()
  {
    return "rtabmap_msgs/GetMap";
  }

  static const char* value(const ::rtabmap_msgs::GetMap&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::GetMapRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GetMap >
template<>
struct MD5Sum< ::rtabmap_msgs::GetMapRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GetMap >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetMapRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GetMapRequest> should match
// service_traits::DataType< ::rtabmap_msgs::GetMap >
template<>
struct DataType< ::rtabmap_msgs::GetMapRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GetMap >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetMapRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::GetMapResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GetMap >
template<>
struct MD5Sum< ::rtabmap_msgs::GetMapResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GetMap >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetMapResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GetMapResponse> should match
// service_traits::DataType< ::rtabmap_msgs::GetMap >
template<>
struct DataType< ::rtabmap_msgs::GetMapResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GetMap >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetMapResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GETMAP_H
