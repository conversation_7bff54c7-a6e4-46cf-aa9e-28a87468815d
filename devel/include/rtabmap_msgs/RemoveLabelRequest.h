// Generated by gencpp from file rtabmap_msgs/RemoveLabelRequest.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_REMOVELABELREQUEST_H
#define RTABMAP_MSGS_MESSAGE_REMOVELABELREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct RemoveLabelRequest_
{
  typedef RemoveLabelRequest_<ContainerAllocator> Type;

  RemoveLabelRequest_()
    : label()  {
    }
  RemoveLabelRequest_(const ContainerAllocator& _alloc)
    : label(_alloc)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _label_type;
  _label_type label;





  typedef boost::shared_ptr< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> const> ConstPtr;

}; // struct RemoveLabelRequest_

typedef ::rtabmap_msgs::RemoveLabelRequest_<std::allocator<void> > RemoveLabelRequest;

typedef boost::shared_ptr< ::rtabmap_msgs::RemoveLabelRequest > RemoveLabelRequestPtr;
typedef boost::shared_ptr< ::rtabmap_msgs::RemoveLabelRequest const> RemoveLabelRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator2> & rhs)
{
  return lhs.label == rhs.label;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "ea23f97416b04c6151d2b576c0665ac1";
  }

  static const char* value(const ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xea23f97416b04c61ULL;
  static const uint64_t static_value2 = 0x51d2b576c0665ac1ULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/RemoveLabelRequest";
  }

  static const char* value(const ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "#request\n"
"string label\n"
;
  }

  static const char* value(const ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.label);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct RemoveLabelRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::RemoveLabelRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "label: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.label);
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_REMOVELABELREQUEST_H
