// Generated by gencpp from file rtabmap_msgs/ResetPose.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_RESETPOSE_H
#define RTABMAP_MSGS_MESSAGE_RESETPOSE_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/ResetPoseRequest.h>
#include <rtabmap_msgs/ResetPoseResponse.h>


namespace rtabmap_msgs
{

struct ResetPose
{

typedef ResetPoseRequest Request;
typedef ResetPoseResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct ResetPose
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::ResetPose > {
  static const char* value()
  {
    return "3d5697f0c9e8859325238b57521f8f4e";
  }

  static const char* value(const ::rtabmap_msgs::ResetPose&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::ResetPose > {
  static const char* value()
  {
    return "rtabmap_msgs/ResetPose";
  }

  static const char* value(const ::rtabmap_msgs::ResetPose&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::ResetPoseRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::ResetPose >
template<>
struct MD5Sum< ::rtabmap_msgs::ResetPoseRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::ResetPose >::value();
  }
  static const char* value(const ::rtabmap_msgs::ResetPoseRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::ResetPoseRequest> should match
// service_traits::DataType< ::rtabmap_msgs::ResetPose >
template<>
struct DataType< ::rtabmap_msgs::ResetPoseRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::ResetPose >::value();
  }
  static const char* value(const ::rtabmap_msgs::ResetPoseRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::ResetPoseResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::ResetPose >
template<>
struct MD5Sum< ::rtabmap_msgs::ResetPoseResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::ResetPose >::value();
  }
  static const char* value(const ::rtabmap_msgs::ResetPoseResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::ResetPoseResponse> should match
// service_traits::DataType< ::rtabmap_msgs::ResetPose >
template<>
struct DataType< ::rtabmap_msgs::ResetPoseResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::ResetPose >::value();
  }
  static const char* value(const ::rtabmap_msgs::ResetPoseResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_RESETPOSE_H
