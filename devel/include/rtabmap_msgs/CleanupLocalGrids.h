// Generated by gencpp from file rtabmap_msgs/CleanupLocalGrids.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_CLEANUPLOCALGRIDS_H
#define RTABMAP_MSGS_MESSAGE_CLEANUPLOCALGRIDS_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/CleanupLocalGridsRequest.h>
#include <rtabmap_msgs/CleanupLocalGridsResponse.h>


namespace rtabmap_msgs
{

struct CleanupLocalGrids
{

typedef CleanupLocalGridsRequest Request;
typedef CleanupLocalGridsResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct CleanupLocalGrids
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::CleanupLocalGrids > {
  static const char* value()
  {
    return "9b90cbbb6fac56a143df9416b6e1fa37";
  }

  static const char* value(const ::rtabmap_msgs::CleanupLocalGrids&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::CleanupLocalGrids > {
  static const char* value()
  {
    return "rtabmap_msgs/CleanupLocalGrids";
  }

  static const char* value(const ::rtabmap_msgs::CleanupLocalGrids&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::CleanupLocalGridsRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::CleanupLocalGrids >
template<>
struct MD5Sum< ::rtabmap_msgs::CleanupLocalGridsRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::CleanupLocalGrids >::value();
  }
  static const char* value(const ::rtabmap_msgs::CleanupLocalGridsRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::CleanupLocalGridsRequest> should match
// service_traits::DataType< ::rtabmap_msgs::CleanupLocalGrids >
template<>
struct DataType< ::rtabmap_msgs::CleanupLocalGridsRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::CleanupLocalGrids >::value();
  }
  static const char* value(const ::rtabmap_msgs::CleanupLocalGridsRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::CleanupLocalGridsResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::CleanupLocalGrids >
template<>
struct MD5Sum< ::rtabmap_msgs::CleanupLocalGridsResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::CleanupLocalGrids >::value();
  }
  static const char* value(const ::rtabmap_msgs::CleanupLocalGridsResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::CleanupLocalGridsResponse> should match
// service_traits::DataType< ::rtabmap_msgs::CleanupLocalGrids >
template<>
struct DataType< ::rtabmap_msgs::CleanupLocalGridsResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::CleanupLocalGrids >::value();
  }
  static const char* value(const ::rtabmap_msgs::CleanupLocalGridsResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_CLEANUPLOCALGRIDS_H
