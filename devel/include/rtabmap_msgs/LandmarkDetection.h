// Generated by gencpp from file rtabmap_msgs/LandmarkDetection.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_LANDMARKDETECTION_H
#define RTABMAP_MSGS_MESSAGE_LANDMARKDETECTION_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/PoseWithCovariance.h>

namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct LandmarkDetection_
{
  typedef LandmarkDetection_<ContainerAllocator> Type;

  LandmarkDetection_()
    : header()
    , landmark_frame_id()
    , id(0)
    , size(0.0)
    , pose()  {
    }
  LandmarkDetection_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , landmark_frame_id(_alloc)
    , id(0)
    , size(0.0)
    , pose(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _landmark_frame_id_type;
  _landmark_frame_id_type landmark_frame_id;

   typedef int32_t _id_type;
  _id_type id;

   typedef float _size_type;
  _size_type size;

   typedef  ::geometry_msgs::PoseWithCovariance_<ContainerAllocator>  _pose_type;
  _pose_type pose;





  typedef boost::shared_ptr< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> const> ConstPtr;

}; // struct LandmarkDetection_

typedef ::rtabmap_msgs::LandmarkDetection_<std::allocator<void> > LandmarkDetection;

typedef boost::shared_ptr< ::rtabmap_msgs::LandmarkDetection > LandmarkDetectionPtr;
typedef boost::shared_ptr< ::rtabmap_msgs::LandmarkDetection const> LandmarkDetectionConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.landmark_frame_id == rhs.landmark_frame_id &&
    lhs.id == rhs.id &&
    lhs.size == rhs.size &&
    lhs.pose == rhs.pose;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> >
{
  static const char* value()
  {
    return "4e910c904de3409be6c1521a98c85c31";
  }

  static const char* value(const ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x4e910c904de3409bULL;
  static const uint64_t static_value2 = 0xe6c1521a98c85c31ULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/LandmarkDetection";
  }

  static const char* value(const ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"# header.stamp: the timestamp of the detection (e.g. image timestamp)\n"
"# header.frame_id: the base frame of pose (e.g., camera optical frame)\n"
"std_msgs/Header header\n"
"\n"
"# Landmark's frame id\n"
"string landmark_frame_id\n"
"\n"
"# Landmark's unique ID: should be >0\n"
"int32 id\n"
"\n"
"# Size in meters of the landmark/tag (optional, set 0 to not use it).\n"
"float32 size\n"
"\n"
"# Pose of the landmark in header.frame_id frame.\n"
"# If covariance is unknown, keep it as null matrix.\n"
"# rtabmap_slam/rtabmap's landmark_angular_variance and\n"
"# landmark_linear_variance parameters can be used\n"
"# for convenience if covariance is null.\n"
"geometry_msgs/PoseWithCovariance pose\n"
"\n"
"\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseWithCovariance\n"
"# This represents a pose in free space with uncertainty.\n"
"\n"
"Pose pose\n"
"\n"
"# Row-major representation of the 6x6 covariance matrix\n"
"# The orientation parameters use a fixed-axis representation.\n"
"# In order, the parameters are:\n"
"# (x, y, z, rotation about X axis, rotation about Y axis, rotation about Z axis)\n"
"float64[36] covariance\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.landmark_frame_id);
      stream.next(m.id);
      stream.next(m.size);
      stream.next(m.pose);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct LandmarkDetection_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::LandmarkDetection_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "landmark_frame_id: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.landmark_frame_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "size: ";
    Printer<float>::stream(s, indent + "  ", v.size);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pose: ";
    Printer< ::geometry_msgs::PoseWithCovariance_<ContainerAllocator> >::stream(s, indent + "  ", v.pose);
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_LANDMARKDETECTION_H
