// Generated by gencpp from file rtabmap_msgs/RemoveLabel.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_REMOVELABEL_H
#define RTABMAP_MSGS_MESSAGE_REMOVELABEL_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/RemoveLabelRequest.h>
#include <rtabmap_msgs/RemoveLabelResponse.h>


namespace rtabmap_msgs
{

struct RemoveLabel
{

typedef RemoveLabelRequest Request;
typedef RemoveLabelResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct RemoveLabel
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::RemoveLabel > {
  static const char* value()
  {
    return "ea23f97416b04c6151d2b576c0665ac1";
  }

  static const char* value(const ::rtabmap_msgs::RemoveLabel&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::RemoveLabel > {
  static const char* value()
  {
    return "rtabmap_msgs/RemoveLabel";
  }

  static const char* value(const ::rtabmap_msgs::RemoveLabel&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::RemoveLabelRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::RemoveLabel >
template<>
struct MD5Sum< ::rtabmap_msgs::RemoveLabelRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::RemoveLabel >::value();
  }
  static const char* value(const ::rtabmap_msgs::RemoveLabelRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::RemoveLabelRequest> should match
// service_traits::DataType< ::rtabmap_msgs::RemoveLabel >
template<>
struct DataType< ::rtabmap_msgs::RemoveLabelRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::RemoveLabel >::value();
  }
  static const char* value(const ::rtabmap_msgs::RemoveLabelRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::RemoveLabelResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::RemoveLabel >
template<>
struct MD5Sum< ::rtabmap_msgs::RemoveLabelResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::RemoveLabel >::value();
  }
  static const char* value(const ::rtabmap_msgs::RemoveLabelResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::RemoveLabelResponse> should match
// service_traits::DataType< ::rtabmap_msgs::RemoveLabel >
template<>
struct DataType< ::rtabmap_msgs::RemoveLabelResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::RemoveLabel >::value();
  }
  static const char* value(const ::rtabmap_msgs::RemoveLabelResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_REMOVELABEL_H
