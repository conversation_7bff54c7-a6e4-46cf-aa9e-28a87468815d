// Generated by gencpp from file rtabmap_msgs/GetMap2.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GETMAP2_H
#define RTABMAP_MSGS_MESSAGE_GETMAP2_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/GetMap2Request.h>
#include <rtabmap_msgs/GetMap2Response.h>


namespace rtabmap_msgs
{

struct GetMap2
{

typedef GetMap2Request Request;
typedef GetMap2Response Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetMap2
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::GetMap2 > {
  static const char* value()
  {
    return "3e23f9a253a7dd6dbe67701cee06fea3";
  }

  static const char* value(const ::rtabmap_msgs::GetMap2&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::GetMap2 > {
  static const char* value()
  {
    return "rtabmap_msgs/GetMap2";
  }

  static const char* value(const ::rtabmap_msgs::GetMap2&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::GetMap2Request> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GetMap2 >
template<>
struct MD5Sum< ::rtabmap_msgs::GetMap2Request>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GetMap2 >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetMap2Request&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GetMap2Request> should match
// service_traits::DataType< ::rtabmap_msgs::GetMap2 >
template<>
struct DataType< ::rtabmap_msgs::GetMap2Request>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GetMap2 >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetMap2Request&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::GetMap2Response> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GetMap2 >
template<>
struct MD5Sum< ::rtabmap_msgs::GetMap2Response>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GetMap2 >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetMap2Response&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GetMap2Response> should match
// service_traits::DataType< ::rtabmap_msgs::GetMap2 >
template<>
struct DataType< ::rtabmap_msgs::GetMap2Response>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GetMap2 >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetMap2Response&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GETMAP2_H
