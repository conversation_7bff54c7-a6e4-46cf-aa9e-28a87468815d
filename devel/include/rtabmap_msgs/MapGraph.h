// Generated by gencpp from file rtabmap_msgs/MapGraph.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_MAPGRAPH_H
#define RTABMAP_MSGS_MESSAGE_MAPGRAPH_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/Transform.h>
#include <geometry_msgs/Pose.h>
#include <rtabmap_msgs/Link.h>

namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct MapGraph_
{
  typedef MapGraph_<ContainerAllocator> Type;

  MapGraph_()
    : header()
    , mapToOdom()
    , posesId()
    , poses()
    , links()  {
    }
  MapGraph_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , mapToOdom(_alloc)
    , posesId(_alloc)
    , poses(_alloc)
    , links(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef  ::geometry_msgs::Transform_<ContainerAllocator>  _mapToOdom_type;
  _mapToOdom_type mapToOdom;

   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _posesId_type;
  _posesId_type posesId;

   typedef std::vector< ::geometry_msgs::Pose_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Pose_<ContainerAllocator> >> _poses_type;
  _poses_type poses;

   typedef std::vector< ::rtabmap_msgs::Link_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::rtabmap_msgs::Link_<ContainerAllocator> >> _links_type;
  _links_type links;





  typedef boost::shared_ptr< ::rtabmap_msgs::MapGraph_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::MapGraph_<ContainerAllocator> const> ConstPtr;

}; // struct MapGraph_

typedef ::rtabmap_msgs::MapGraph_<std::allocator<void> > MapGraph;

typedef boost::shared_ptr< ::rtabmap_msgs::MapGraph > MapGraphPtr;
typedef boost::shared_ptr< ::rtabmap_msgs::MapGraph const> MapGraphConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::MapGraph_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::MapGraph_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::MapGraph_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.mapToOdom == rhs.mapToOdom &&
    lhs.posesId == rhs.posesId &&
    lhs.poses == rhs.poses &&
    lhs.links == rhs.links;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::MapGraph_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::MapGraph_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::MapGraph_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::MapGraph_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::MapGraph_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >
{
  static const char* value()
  {
    return "903c2e44ed7e275e08ef323188cb38ee";
  }

  static const char* value(const ::rtabmap_msgs::MapGraph_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x903c2e44ed7e275eULL;
  static const uint64_t static_value2 = 0x08ef323188cb38eeULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/MapGraph";
  }

  static const char* value(const ::rtabmap_msgs::MapGraph_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"Header header\n"
"\n"
"##\n"
"# /map to /odom transform\n"
"# Always identity when the graph is optimized from the latest pose.\n"
"##\n"
"geometry_msgs/Transform mapToOdom\n"
"\n"
"# The poses\n"
"int32[] posesId\n"
"geometry_msgs/Pose[] poses\n"
"\n"
"# The links\n"
"Link[] links\n"
"\n"
"\n"
"\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Transform\n"
"# This represents the transform between two coordinate frames in free space.\n"
"\n"
"Vector3 translation\n"
"Quaternion rotation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: rtabmap_msgs/Link\n"
"#class rtabmap::Link\n"
"#{\n"
"#   int from;\n"
"#   int to;\n"
"#   Type type;\n"
"#   Transform transform;\n"
"#   cv::Mat(6,6,CV_64FC1) information;\n"
"#}\n"
"\n"
"int32 fromId\n"
"int32 toId\n"
"int32 type\n"
"geometry_msgs/Transform transform\n"
"float64[36] information\n"
;
  }

  static const char* value(const ::rtabmap_msgs::MapGraph_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.mapToOdom);
      stream.next(m.posesId);
      stream.next(m.poses);
      stream.next(m.links);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct MapGraph_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::MapGraph_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::MapGraph_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "mapToOdom: ";
    Printer< ::geometry_msgs::Transform_<ContainerAllocator> >::stream(s, indent + "  ", v.mapToOdom);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "posesId: ";
    if (v.posesId.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.posesId.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.posesId[i]);
    }
    if (v.posesId.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "poses: ";
    if (v.poses.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.poses.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Pose_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.poses[i]);
    }
    if (v.poses.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "links: ";
    if (v.links.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.links.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::rtabmap_msgs::Link_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.links[i]);
    }
    if (v.links.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_MAPGRAPH_H
