// Generated by gencpp from file rtabmap_msgs/AddLink.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_ADDLINK_H
#define RTABMAP_MSGS_MESSAGE_ADDLINK_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/AddLinkRequest.h>
#include <rtabmap_msgs/AddLinkResponse.h>


namespace rtabmap_msgs
{

struct AddLink
{

typedef AddLinkRequest Request;
typedef AddLinkResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct AddLink
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::AddLink > {
  static const char* value()
  {
    return "050728e3c25a15bf78d487d6aafab152";
  }

  static const char* value(const ::rtabmap_msgs::AddLink&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::AddLink > {
  static const char* value()
  {
    return "rtabmap_msgs/AddLink";
  }

  static const char* value(const ::rtabmap_msgs::AddLink&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::AddLinkRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::AddLink >
template<>
struct MD5Sum< ::rtabmap_msgs::AddLinkRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::AddLink >::value();
  }
  static const char* value(const ::rtabmap_msgs::AddLinkRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::AddLinkRequest> should match
// service_traits::DataType< ::rtabmap_msgs::AddLink >
template<>
struct DataType< ::rtabmap_msgs::AddLinkRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::AddLink >::value();
  }
  static const char* value(const ::rtabmap_msgs::AddLinkRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::AddLinkResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::AddLink >
template<>
struct MD5Sum< ::rtabmap_msgs::AddLinkResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::AddLink >::value();
  }
  static const char* value(const ::rtabmap_msgs::AddLinkResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::AddLinkResponse> should match
// service_traits::DataType< ::rtabmap_msgs::AddLink >
template<>
struct DataType< ::rtabmap_msgs::AddLinkResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::AddLink >::value();
  }
  static const char* value(const ::rtabmap_msgs::AddLinkResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_ADDLINK_H
