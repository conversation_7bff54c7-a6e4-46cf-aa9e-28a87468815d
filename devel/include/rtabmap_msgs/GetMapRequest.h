// Generated by gencpp from file rtabmap_msgs/GetMapRequest.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GETMAPREQUEST_H
#define RTABMAP_MSGS_MESSAGE_GETMAPREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct GetMapRequest_
{
  typedef GetMapRequest_<ContainerAllocator> Type;

  GetMapRequest_()
    : global(false)
    , optimized(false)
    , graphOnly(false)  {
    }
  GetMapRequest_(const ContainerAllocator& _alloc)
    : global(false)
    , optimized(false)
    , graphOnly(false)  {
  (void)_alloc;
    }



   typedef uint8_t _global_type;
  _global_type global;

   typedef uint8_t _optimized_type;
  _optimized_type optimized;

   typedef uint8_t _graphOnly_type;
  _graphOnly_type graphOnly;





  typedef boost::shared_ptr< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> const> ConstPtr;

}; // struct GetMapRequest_

typedef ::rtabmap_msgs::GetMapRequest_<std::allocator<void> > GetMapRequest;

typedef boost::shared_ptr< ::rtabmap_msgs::GetMapRequest > GetMapRequestPtr;
typedef boost::shared_ptr< ::rtabmap_msgs::GetMapRequest const> GetMapRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::GetMapRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GetMapRequest_<ContainerAllocator2> & rhs)
{
  return lhs.global == rhs.global &&
    lhs.optimized == rhs.optimized &&
    lhs.graphOnly == rhs.graphOnly;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::GetMapRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GetMapRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "6213f9f13cced23f4d224b22f59d839c";
  }

  static const char* value(const ::rtabmap_msgs::GetMapRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x6213f9f13cced23fULL;
  static const uint64_t static_value2 = 0x4d224b22f59d839cULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/GetMapRequest";
  }

  static const char* value(const ::rtabmap_msgs::GetMapRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "#request\n"
"bool global\n"
"bool optimized\n"
"bool graphOnly\n"
;
  }

  static const char* value(const ::rtabmap_msgs::GetMapRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.global);
      stream.next(m.optimized);
      stream.next(m.graphOnly);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetMapRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::GetMapRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::GetMapRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "global: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.global);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "optimized: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.optimized);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "graphOnly: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.graphOnly);
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GETMAPREQUEST_H
