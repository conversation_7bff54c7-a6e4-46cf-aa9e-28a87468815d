// Generated by gencpp from file rtabmap_msgs/SetGoal.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_SETGOAL_H
#define RTABMAP_MSGS_MESSAGE_SETGOAL_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/SetGoalRequest.h>
#include <rtabmap_msgs/SetGoalResponse.h>


namespace rtabmap_msgs
{

struct SetGoal
{

typedef SetGoalRequest Request;
typedef SetGoalResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct SetGoal
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::SetGoal > {
  static const char* value()
  {
    return "9ab4a370e8733a44f7bff8c581f9c5bf";
  }

  static const char* value(const ::rtabmap_msgs::SetGoal&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::SetGoal > {
  static const char* value()
  {
    return "rtabmap_msgs/SetGoal";
  }

  static const char* value(const ::rtabmap_msgs::SetGoal&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::SetGoalRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::SetGoal >
template<>
struct MD5Sum< ::rtabmap_msgs::SetGoalRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::SetGoal >::value();
  }
  static const char* value(const ::rtabmap_msgs::SetGoalRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::SetGoalRequest> should match
// service_traits::DataType< ::rtabmap_msgs::SetGoal >
template<>
struct DataType< ::rtabmap_msgs::SetGoalRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::SetGoal >::value();
  }
  static const char* value(const ::rtabmap_msgs::SetGoalRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::SetGoalResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::SetGoal >
template<>
struct MD5Sum< ::rtabmap_msgs::SetGoalResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::SetGoal >::value();
  }
  static const char* value(const ::rtabmap_msgs::SetGoalResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::SetGoalResponse> should match
// service_traits::DataType< ::rtabmap_msgs::SetGoal >
template<>
struct DataType< ::rtabmap_msgs::SetGoalResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::SetGoal >::value();
  }
  static const char* value(const ::rtabmap_msgs::SetGoalResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_SETGOAL_H
