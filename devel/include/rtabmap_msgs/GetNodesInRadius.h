// Generated by gencpp from file rtabmap_msgs/GetNodesInRadius.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GETNODESINRADIUS_H
#define RTABMAP_MSGS_MESSAGE_GETNODESINRADIUS_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/GetNodesInRadiusRequest.h>
#include <rtabmap_msgs/GetNodesInRadiusResponse.h>


namespace rtabmap_msgs
{

struct GetNodesInRadius
{

typedef GetNodesInRadiusRequest Request;
typedef GetNodesInRadiusResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetNodesInRadius
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::GetNodesInRadius > {
  static const char* value()
  {
    return "c58f0c4a9564063f02e33810c1c0f386";
  }

  static const char* value(const ::rtabmap_msgs::GetNodesInRadius&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::GetNodesInRadius > {
  static const char* value()
  {
    return "rtabmap_msgs/GetNodesInRadius";
  }

  static const char* value(const ::rtabmap_msgs::GetNodesInRadius&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::GetNodesInRadiusRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GetNodesInRadius >
template<>
struct MD5Sum< ::rtabmap_msgs::GetNodesInRadiusRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GetNodesInRadius >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetNodesInRadiusRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GetNodesInRadiusRequest> should match
// service_traits::DataType< ::rtabmap_msgs::GetNodesInRadius >
template<>
struct DataType< ::rtabmap_msgs::GetNodesInRadiusRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GetNodesInRadius >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetNodesInRadiusRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::GetNodesInRadiusResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GetNodesInRadius >
template<>
struct MD5Sum< ::rtabmap_msgs::GetNodesInRadiusResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GetNodesInRadius >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetNodesInRadiusResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GetNodesInRadiusResponse> should match
// service_traits::DataType< ::rtabmap_msgs::GetNodesInRadius >
template<>
struct DataType< ::rtabmap_msgs::GetNodesInRadiusResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GetNodesInRadius >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetNodesInRadiusResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GETNODESINRADIUS_H
