// Generated by gencpp from file rtabmap_msgs/GetNodeDataRequest.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GETNODEDATAREQUEST_H
#define RTABMAP_MSGS_MESSAGE_GETNODEDATAREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct GetNodeDataRequest_
{
  typedef GetNodeDataRequest_<ContainerAllocator> Type;

  GetNodeDataRequest_()
    : ids()
    , images(false)
    , scan(false)
    , grid(false)
    , user_data(false)  {
    }
  GetNodeDataRequest_(const ContainerAllocator& _alloc)
    : ids(_alloc)
    , images(false)
    , scan(false)
    , grid(false)
    , user_data(false)  {
  (void)_alloc;
    }



   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _ids_type;
  _ids_type ids;

   typedef uint8_t _images_type;
  _images_type images;

   typedef uint8_t _scan_type;
  _scan_type scan;

   typedef uint8_t _grid_type;
  _grid_type grid;

   typedef uint8_t _user_data_type;
  _user_data_type user_data;





  typedef boost::shared_ptr< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> const> ConstPtr;

}; // struct GetNodeDataRequest_

typedef ::rtabmap_msgs::GetNodeDataRequest_<std::allocator<void> > GetNodeDataRequest;

typedef boost::shared_ptr< ::rtabmap_msgs::GetNodeDataRequest > GetNodeDataRequestPtr;
typedef boost::shared_ptr< ::rtabmap_msgs::GetNodeDataRequest const> GetNodeDataRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator2> & rhs)
{
  return lhs.ids == rhs.ids &&
    lhs.images == rhs.images &&
    lhs.scan == rhs.scan &&
    lhs.grid == rhs.grid &&
    lhs.user_data == rhs.user_data;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "0e8d18cf9b3249ab98f2d25e6071cc75";
  }

  static const char* value(const ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x0e8d18cf9b3249abULL;
  static const uint64_t static_value2 = 0x98f2d25e6071cc75ULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/GetNodeDataRequest";
  }

  static const char* value(const ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "#request\n"
"int32[] ids\n"
"bool images\n"
"bool scan\n"
"bool grid\n"
"bool user_data\n"
;
  }

  static const char* value(const ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.ids);
      stream.next(m.images);
      stream.next(m.scan);
      stream.next(m.grid);
      stream.next(m.user_data);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetNodeDataRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::GetNodeDataRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "ids: ";
    if (v.ids.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.ids.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.ids[i]);
    }
    if (v.ids.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "images: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.images);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "scan: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.scan);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "grid: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.grid);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "user_data: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.user_data);
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GETNODEDATAREQUEST_H
