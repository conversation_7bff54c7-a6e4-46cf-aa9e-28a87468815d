// Generated by gencpp from file rtabmap_msgs/GlobalBundleAdjustmentRequest.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GLOBALBUNDLEADJUSTMENTREQUEST_H
#define RTABMAP_MSGS_MESSAGE_GLOBALBUNDLEADJUSTMENTREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct GlobalBundleAdjustmentRequest_
{
  typedef GlobalBundleAdjustmentRequest_<ContainerAllocator> Type;

  GlobalBundleAdjustmentRequest_()
    : type(0)
    , iterations(0)
    , pixel_variance(0.0)
    , voc_matches(false)  {
    }
  GlobalBundleAdjustmentRequest_(const ContainerAllocator& _alloc)
    : type(0)
    , iterations(0)
    , pixel_variance(0.0)
    , voc_matches(false)  {
  (void)_alloc;
    }



   typedef int32_t _type_type;
  _type_type type;

   typedef int32_t _iterations_type;
  _iterations_type iterations;

   typedef float _pixel_variance_type;
  _pixel_variance_type pixel_variance;

   typedef uint8_t _voc_matches_type;
  _voc_matches_type voc_matches;





  typedef boost::shared_ptr< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> const> ConstPtr;

}; // struct GlobalBundleAdjustmentRequest_

typedef ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<std::allocator<void> > GlobalBundleAdjustmentRequest;

typedef boost::shared_ptr< ::rtabmap_msgs::GlobalBundleAdjustmentRequest > GlobalBundleAdjustmentRequestPtr;
typedef boost::shared_ptr< ::rtabmap_msgs::GlobalBundleAdjustmentRequest const> GlobalBundleAdjustmentRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator2> & rhs)
{
  return lhs.type == rhs.type &&
    lhs.iterations == rhs.iterations &&
    lhs.pixel_variance == rhs.pixel_variance &&
    lhs.voc_matches == rhs.voc_matches;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "2e1ed640ba9f481fdaa2226734d70674";
  }

  static const char* value(const ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x2e1ed640ba9f481fULL;
  static const uint64_t static_value2 = 0xdaa2226734d70674ULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/GlobalBundleAdjustmentRequest";
  }

  static const char* value(const ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# Global Bundle Adjustment service\n"
"#\n"
"#       Perform global bundle adjustment. Note that as soon as the map \n"
"#       is modified again, the graph is re-optimized the standard way (without SBA).\n"
"#       It then makes only sense to use this after a mapping run (and after a call \n"
"#       to /rtabmap/pause) when you know that the robot will restart in localization \n"
"#       mode the next time, or at the beginning of the localization session.\n"
"#\n"
"\n"
"# Optimizer type (0=g2o, 1=CVSBA), default 0\n"
"int32 type\n"
"\n"
"# Iterations,        default 0 (use Optimizer/Iterations already loaded in the node)\n"
"int32 iterations\n"
"\n"
"# Pixel variance,    default 0 (use g2o/PixelVariance already loaded in the node)\n"
"float32 pixel_variance\n"
"\n"
"# Use vocabulary matches, default false (rematch all features between frames)\n"
"bool voc_matches\n"
;
  }

  static const char* value(const ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.type);
      stream.next(m.iterations);
      stream.next(m.pixel_variance);
      stream.next(m.voc_matches);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GlobalBundleAdjustmentRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::GlobalBundleAdjustmentRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "type: ";
    Printer<int32_t>::stream(s, indent + "  ", v.type);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "iterations: ";
    Printer<int32_t>::stream(s, indent + "  ", v.iterations);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pixel_variance: ";
    Printer<float>::stream(s, indent + "  ", v.pixel_variance);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "voc_matches: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.voc_matches);
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GLOBALBUNDLEADJUSTMENTREQUEST_H
