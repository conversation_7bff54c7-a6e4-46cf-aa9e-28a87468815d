// Generated by gencpp from file rtabmap_msgs/CleanupLocalGridsRequest.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_CLEANUPLOCALGRIDSREQUEST_H
#define RTABMAP_MSGS_MESSAGE_CLEANUPLOCALGRIDSREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct CleanupLocalGridsRequest_
{
  typedef CleanupLocalGridsRequest_<ContainerAllocator> Type;

  CleanupLocalGridsRequest_()
    : radius(0)
    , filter_scans(false)  {
    }
  CleanupLocalGridsRequest_(const ContainerAllocator& _alloc)
    : radius(0)
    , filter_scans(false)  {
  (void)_alloc;
    }



   typedef int32_t _radius_type;
  _radius_type radius;

   typedef uint8_t _filter_scans_type;
  _filter_scans_type filter_scans;





  typedef boost::shared_ptr< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> const> ConstPtr;

}; // struct CleanupLocalGridsRequest_

typedef ::rtabmap_msgs::CleanupLocalGridsRequest_<std::allocator<void> > CleanupLocalGridsRequest;

typedef boost::shared_ptr< ::rtabmap_msgs::CleanupLocalGridsRequest > CleanupLocalGridsRequestPtr;
typedef boost::shared_ptr< ::rtabmap_msgs::CleanupLocalGridsRequest const> CleanupLocalGridsRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator2> & rhs)
{
  return lhs.radius == rhs.radius &&
    lhs.filter_scans == rhs.filter_scans;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "f7e9883a6b75c0ecc1bb1c33f3332f61";
  }

  static const char* value(const ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xf7e9883a6b75c0ecULL;
  static const uint64_t static_value2 = 0xc1bb1c33f3332f61ULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/CleanupLocalGridsRequest";
  }

  static const char* value(const ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "#  Cleanup local grids service\n"
"# \n"
"#     Clear empty space from local occupancy grids \n"
"#     (and laser scans) based on the current optimized global 2d grid map.\n"
"#     If the map needs to be regenerated in the future (e.g., when\n"
"#     we re-use the map in SLAM mode), removed obstacles won't reappear.\n"
"#     Use this with care and only when you know that the map doesn't have errors, \n"
"#     otherwise some real obstacles/walls may be cleared if there is too much \n"
"#     drift in the map.\n"
"#\n"
"\n"
"# Radius in cells around empty cell without obstacles to clear underlying obstacles, default 1 cell if not set.\n"
"int32 radius\n"
"\n"
"# Filter also the scans, default false if not set.\n"
"# The filtered laser scans will be used for localization,\n"
"# so if dynamic obstacles have been removed, localization won't try to\n"
"# match them anymore. Filtering the laser scans cannot be reverted, \n"
"# but grids can (see DatabaseViewer->Edit menu).\n"
"bool filter_scans\n"
"\n"
;
  }

  static const char* value(const ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.radius);
      stream.next(m.filter_scans);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct CleanupLocalGridsRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::CleanupLocalGridsRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "radius: ";
    Printer<int32_t>::stream(s, indent + "  ", v.radius);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "filter_scans: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.filter_scans);
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_CLEANUPLOCALGRIDSREQUEST_H
