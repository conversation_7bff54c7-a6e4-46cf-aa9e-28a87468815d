// Generated by gencpp from file rtabmap_msgs/GPS.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GPS_H
#define RTABMAP_MSGS_MESSAGE_GPS_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct GPS_
{
  typedef GPS_<ContainerAllocator> Type;

  GPS_()
    : stamp(0.0)
    , longitude(0.0)
    , latitude(0.0)
    , altitude(0.0)
    , error(0.0)
    , bearing(0.0)  {
    }
  GPS_(const ContainerAllocator& _alloc)
    : stamp(0.0)
    , longitude(0.0)
    , latitude(0.0)
    , altitude(0.0)
    , error(0.0)
    , bearing(0.0)  {
  (void)_alloc;
    }



   typedef double _stamp_type;
  _stamp_type stamp;

   typedef double _longitude_type;
  _longitude_type longitude;

   typedef double _latitude_type;
  _latitude_type latitude;

   typedef double _altitude_type;
  _altitude_type altitude;

   typedef double _error_type;
  _error_type error;

   typedef double _bearing_type;
  _bearing_type bearing;





  typedef boost::shared_ptr< ::rtabmap_msgs::GPS_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::GPS_<ContainerAllocator> const> ConstPtr;

}; // struct GPS_

typedef ::rtabmap_msgs::GPS_<std::allocator<void> > GPS;

typedef boost::shared_ptr< ::rtabmap_msgs::GPS > GPSPtr;
typedef boost::shared_ptr< ::rtabmap_msgs::GPS const> GPSConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::GPS_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::GPS_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::GPS_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GPS_<ContainerAllocator2> & rhs)
{
  return lhs.stamp == rhs.stamp &&
    lhs.longitude == rhs.longitude &&
    lhs.latitude == rhs.latitude &&
    lhs.altitude == rhs.altitude &&
    lhs.error == rhs.error &&
    lhs.bearing == rhs.bearing;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::GPS_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GPS_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GPS_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GPS_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GPS_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GPS_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GPS_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GPS_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::GPS_<ContainerAllocator> >
{
  static const char* value()
  {
    return "0acde0d09a1ab74993cf4e41ff4eae49";
  }

  static const char* value(const ::rtabmap_msgs::GPS_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x0acde0d09a1ab749ULL;
  static const uint64_t static_value2 = 0x93cf4e41ff4eae49ULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::GPS_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/GPS";
  }

  static const char* value(const ::rtabmap_msgs::GPS_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::GPS_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"float64 stamp      # in seconds\n"
"float64 longitude  # DD format\n"
"float64 latitude   # DD format\n"
"float64 altitude   # in meters\n"
"float64 error      # in meters\n"
"float64 bearing    # North 0->360 deg\n"
;
  }

  static const char* value(const ::rtabmap_msgs::GPS_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::GPS_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.stamp);
      stream.next(m.longitude);
      stream.next(m.latitude);
      stream.next(m.altitude);
      stream.next(m.error);
      stream.next(m.bearing);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GPS_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::GPS_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::GPS_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "stamp: ";
    Printer<double>::stream(s, indent + "  ", v.stamp);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "longitude: ";
    Printer<double>::stream(s, indent + "  ", v.longitude);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "latitude: ";
    Printer<double>::stream(s, indent + "  ", v.latitude);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "altitude: ";
    Printer<double>::stream(s, indent + "  ", v.altitude);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "error: ";
    Printer<double>::stream(s, indent + "  ", v.error);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "bearing: ";
    Printer<double>::stream(s, indent + "  ", v.bearing);
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GPS_H
