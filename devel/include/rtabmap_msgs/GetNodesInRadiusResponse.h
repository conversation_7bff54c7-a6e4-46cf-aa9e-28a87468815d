// Generated by gencpp from file rtabmap_msgs/GetNodesInRadiusResponse.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GETNODESINRADIUSRESPONSE_H
#define RTABMAP_MSGS_MESSAGE_GETNODESINRADIUSRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Pose.h>

namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct GetNodesInRadiusResponse_
{
  typedef GetNodesInRadiusResponse_<ContainerAllocator> Type;

  GetNodesInRadiusResponse_()
    : ids()
    , poses()
    , distsSqr()  {
    }
  GetNodesInRadiusResponse_(const ContainerAllocator& _alloc)
    : ids(_alloc)
    , poses(_alloc)
    , distsSqr(_alloc)  {
  (void)_alloc;
    }



   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _ids_type;
  _ids_type ids;

   typedef std::vector< ::geometry_msgs::Pose_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Pose_<ContainerAllocator> >> _poses_type;
  _poses_type poses;

   typedef std::vector<float, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<float>> _distsSqr_type;
  _distsSqr_type distsSqr;





  typedef boost::shared_ptr< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> const> ConstPtr;

}; // struct GetNodesInRadiusResponse_

typedef ::rtabmap_msgs::GetNodesInRadiusResponse_<std::allocator<void> > GetNodesInRadiusResponse;

typedef boost::shared_ptr< ::rtabmap_msgs::GetNodesInRadiusResponse > GetNodesInRadiusResponsePtr;
typedef boost::shared_ptr< ::rtabmap_msgs::GetNodesInRadiusResponse const> GetNodesInRadiusResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator2> & rhs)
{
  return lhs.ids == rhs.ids &&
    lhs.poses == rhs.poses &&
    lhs.distsSqr == rhs.distsSqr;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "1b8b2d45b8ca77eac8d3c9b89916ddbf";
  }

  static const char* value(const ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x1b8b2d45b8ca77eaULL;
  static const uint64_t static_value2 = 0xc8d3c9b89916ddbfULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/GetNodesInRadiusResponse";
  }

  static const char* value(const ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "#response\n"
"int32[] ids\n"
"geometry_msgs/Pose[] poses\n"
"float32[] distsSqr\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.ids);
      stream.next(m.poses);
      stream.next(m.distsSqr);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetNodesInRadiusResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::GetNodesInRadiusResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "ids: ";
    if (v.ids.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.ids.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.ids[i]);
    }
    if (v.ids.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "poses: ";
    if (v.poses.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.poses.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Pose_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.poses[i]);
    }
    if (v.poses.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "distsSqr: ";
    if (v.distsSqr.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.distsSqr.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.distsSqr[i]);
    }
    if (v.distsSqr.empty() || true)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GETNODESINRADIUSRESPONSE_H
