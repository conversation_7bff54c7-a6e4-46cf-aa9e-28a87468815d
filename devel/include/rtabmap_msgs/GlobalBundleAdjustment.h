// Generated by gencpp from file rtabmap_msgs/GlobalBundleAdjustment.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GLOBALBUNDLEADJUSTMENT_H
#define RTABMAP_MSGS_MESSAGE_GLOBALBUNDLEADJUSTMENT_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/GlobalBundleAdjustmentRequest.h>
#include <rtabmap_msgs/GlobalBundleAdjustmentResponse.h>


namespace rtabmap_msgs
{

struct GlobalBundleAdjustment
{

typedef GlobalBundleAdjustmentRequest Request;
typedef GlobalBundleAdjustmentResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GlobalBundleAdjustment
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustment > {
  static const char* value()
  {
    return "2e1ed640ba9f481fdaa2226734d70674";
  }

  static const char* value(const ::rtabmap_msgs::GlobalBundleAdjustment&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::GlobalBundleAdjustment > {
  static const char* value()
  {
    return "rtabmap_msgs/GlobalBundleAdjustment";
  }

  static const char* value(const ::rtabmap_msgs::GlobalBundleAdjustment&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustmentRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustment >
template<>
struct MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustmentRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustment >::value();
  }
  static const char* value(const ::rtabmap_msgs::GlobalBundleAdjustmentRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GlobalBundleAdjustmentRequest> should match
// service_traits::DataType< ::rtabmap_msgs::GlobalBundleAdjustment >
template<>
struct DataType< ::rtabmap_msgs::GlobalBundleAdjustmentRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GlobalBundleAdjustment >::value();
  }
  static const char* value(const ::rtabmap_msgs::GlobalBundleAdjustmentRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustmentResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustment >
template<>
struct MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustmentResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GlobalBundleAdjustment >::value();
  }
  static const char* value(const ::rtabmap_msgs::GlobalBundleAdjustmentResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GlobalBundleAdjustmentResponse> should match
// service_traits::DataType< ::rtabmap_msgs::GlobalBundleAdjustment >
template<>
struct DataType< ::rtabmap_msgs::GlobalBundleAdjustmentResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GlobalBundleAdjustment >::value();
  }
  static const char* value(const ::rtabmap_msgs::GlobalBundleAdjustmentResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GLOBALBUNDLEADJUSTMENT_H
