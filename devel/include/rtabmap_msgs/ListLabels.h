// Generated by gencpp from file rtabmap_msgs/ListLabels.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_LISTLABELS_H
#define RTABMAP_MSGS_MESSAGE_LISTLABELS_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/ListLabelsRequest.h>
#include <rtabmap_msgs/ListLabelsResponse.h>


namespace rtabmap_msgs
{

struct ListLabels
{

typedef ListLabelsRequest Request;
typedef ListLabelsResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct ListLabels
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::ListLabels > {
  static const char* value()
  {
    return "eeaf81b9c58c5f0e5150e4704df1159c";
  }

  static const char* value(const ::rtabmap_msgs::ListLabels&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::ListLabels > {
  static const char* value()
  {
    return "rtabmap_msgs/ListLabels";
  }

  static const char* value(const ::rtabmap_msgs::ListLabels&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::ListLabelsRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::ListLabels >
template<>
struct MD5Sum< ::rtabmap_msgs::ListLabelsRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::ListLabels >::value();
  }
  static const char* value(const ::rtabmap_msgs::ListLabelsRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::ListLabelsRequest> should match
// service_traits::DataType< ::rtabmap_msgs::ListLabels >
template<>
struct DataType< ::rtabmap_msgs::ListLabelsRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::ListLabels >::value();
  }
  static const char* value(const ::rtabmap_msgs::ListLabelsRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::ListLabelsResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::ListLabels >
template<>
struct MD5Sum< ::rtabmap_msgs::ListLabelsResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::ListLabels >::value();
  }
  static const char* value(const ::rtabmap_msgs::ListLabelsResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::ListLabelsResponse> should match
// service_traits::DataType< ::rtabmap_msgs::ListLabels >
template<>
struct DataType< ::rtabmap_msgs::ListLabelsResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::ListLabels >::value();
  }
  static const char* value(const ::rtabmap_msgs::ListLabelsResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_LISTLABELS_H
