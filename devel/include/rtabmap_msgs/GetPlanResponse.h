// Generated by gencpp from file rtabmap_msgs/GetPlanResponse.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GETPLANRESPONSE_H
#define RTABMAP_MSGS_MESSAGE_GETPLANRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <rtabmap_msgs/Path.h>

namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct GetPlanResponse_
{
  typedef GetPlanResponse_<ContainerAllocator> Type;

  GetPlanResponse_()
    : plan()  {
    }
  GetPlanResponse_(const ContainerAllocator& _alloc)
    : plan(_alloc)  {
  (void)_alloc;
    }



   typedef  ::rtabmap_msgs::Path_<ContainerAllocator>  _plan_type;
  _plan_type plan;





  typedef boost::shared_ptr< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> const> ConstPtr;

}; // struct GetPlanResponse_

typedef ::rtabmap_msgs::GetPlanResponse_<std::allocator<void> > GetPlanResponse;

typedef boost::shared_ptr< ::rtabmap_msgs::GetPlanResponse > GetPlanResponsePtr;
typedef boost::shared_ptr< ::rtabmap_msgs::GetPlanResponse const> GetPlanResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator2> & rhs)
{
  return lhs.plan == rhs.plan;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "0412b9858bfcee4b2ee4fbf2f8eb5028";
  }

  static const char* value(const ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x0412b9858bfcee4bULL;
  static const uint64_t static_value2 = 0x2ee4fbf2f8eb5028ULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/GetPlanResponse";
  }

  static const char* value(const ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "Path plan\n"
"\n"
"================================================================================\n"
"MSG: rtabmap_msgs/Path\n"
"\n"
"Header header\n"
"\n"
"int32[] nodeIds\n"
"geometry_msgs/Pose[] poses\n"
"\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.plan);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetPlanResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::GetPlanResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "plan: ";
    Printer< ::rtabmap_msgs::Path_<ContainerAllocator> >::stream(s, indent + "  ", v.plan);
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GETPLANRESPONSE_H
