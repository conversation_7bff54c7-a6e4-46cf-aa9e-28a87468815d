// Generated by gencpp from file rtabmap_msgs/ListLabelsResponse.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_LISTLABELSRESPONSE_H
#define RTABMAP_MSGS_MESSAGE_LISTLABELSRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace rtabmap_msgs
{
template <class ContainerAllocator>
struct ListLabelsResponse_
{
  typedef ListLabelsResponse_<ContainerAllocator> Type;

  ListLabelsResponse_()
    : ids()
    , labels()  {
    }
  ListLabelsResponse_(const ContainerAllocator& _alloc)
    : ids(_alloc)
    , labels(_alloc)  {
  (void)_alloc;
    }



   typedef std::vector<int32_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<int32_t>> _ids_type;
  _ids_type ids;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _labels_type;
  _labels_type labels;





  typedef boost::shared_ptr< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> const> ConstPtr;

}; // struct ListLabelsResponse_

typedef ::rtabmap_msgs::ListLabelsResponse_<std::allocator<void> > ListLabelsResponse;

typedef boost::shared_ptr< ::rtabmap_msgs::ListLabelsResponse > ListLabelsResponsePtr;
typedef boost::shared_ptr< ::rtabmap_msgs::ListLabelsResponse const> ListLabelsResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator2> & rhs)
{
  return lhs.ids == rhs.ids &&
    lhs.labels == rhs.labels;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator1> & lhs, const ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rtabmap_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "eeaf81b9c58c5f0e5150e4704df1159c";
  }

  static const char* value(const ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xeeaf81b9c58c5f0eULL;
  static const uint64_t static_value2 = 0x5150e4704df1159cULL;
};

template<class ContainerAllocator>
struct DataType< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rtabmap_msgs/ListLabelsResponse";
  }

  static const char* value(const ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "#response \n"
"int32[] ids\n"
"string[] labels\n"
;
  }

  static const char* value(const ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.ids);
      stream.next(m.labels);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct ListLabelsResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rtabmap_msgs::ListLabelsResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "ids: ";
    if (v.ids.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.ids.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<int32_t>::stream(s, true ? std::string() : indent + "    ", v.ids[i]);
    }
    if (v.ids.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "labels: ";
    if (v.labels.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.labels.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.labels[i]);
    }
    if (v.labels.empty() || true)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_LISTLABELSRESPONSE_H
