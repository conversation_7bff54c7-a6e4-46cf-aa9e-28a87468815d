// Generated by gencpp from file rtabmap_msgs/LoadDatabase.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_LOADDATABASE_H
#define RTABMAP_MSGS_MESSAGE_LOADDATABASE_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/LoadDatabaseRequest.h>
#include <rtabmap_msgs/LoadDatabaseResponse.h>


namespace rtabmap_msgs
{

struct LoadDatabase
{

typedef LoadDatabaseRequest Request;
typedef LoadDatabaseResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct LoadDatabase
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::LoadDatabase > {
  static const char* value()
  {
    return "686abf12c0ee22118c7e5fcc29bcdabe";
  }

  static const char* value(const ::rtabmap_msgs::LoadDatabase&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::LoadDatabase > {
  static const char* value()
  {
    return "rtabmap_msgs/LoadDatabase";
  }

  static const char* value(const ::rtabmap_msgs::LoadDatabase&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::LoadDatabaseRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::LoadDatabase >
template<>
struct MD5Sum< ::rtabmap_msgs::LoadDatabaseRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::LoadDatabase >::value();
  }
  static const char* value(const ::rtabmap_msgs::LoadDatabaseRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::LoadDatabaseRequest> should match
// service_traits::DataType< ::rtabmap_msgs::LoadDatabase >
template<>
struct DataType< ::rtabmap_msgs::LoadDatabaseRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::LoadDatabase >::value();
  }
  static const char* value(const ::rtabmap_msgs::LoadDatabaseRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::LoadDatabaseResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::LoadDatabase >
template<>
struct MD5Sum< ::rtabmap_msgs::LoadDatabaseResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::LoadDatabase >::value();
  }
  static const char* value(const ::rtabmap_msgs::LoadDatabaseResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::LoadDatabaseResponse> should match
// service_traits::DataType< ::rtabmap_msgs::LoadDatabase >
template<>
struct DataType< ::rtabmap_msgs::LoadDatabaseResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::LoadDatabase >::value();
  }
  static const char* value(const ::rtabmap_msgs::LoadDatabaseResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_LOADDATABASE_H
