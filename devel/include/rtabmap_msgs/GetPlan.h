// Generated by gencpp from file rtabmap_msgs/GetPlan.msg
// DO NOT EDIT!


#ifndef RTABMAP_MSGS_MESSAGE_GETPLAN_H
#define RTABMAP_MSGS_MESSAGE_GETPLAN_H

#include <ros/service_traits.h>


#include <rtabmap_msgs/GetPlanRequest.h>
#include <rtabmap_msgs/GetPlanResponse.h>


namespace rtabmap_msgs
{

struct GetPlan
{

typedef GetPlanRequest Request;
typedef GetPlanResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetPlan
} // namespace rtabmap_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rtabmap_msgs::GetPlan > {
  static const char* value()
  {
    return "11e7a6b05e2b36bab483bec069bf36b9";
  }

  static const char* value(const ::rtabmap_msgs::GetPlan&) { return value(); }
};

template<>
struct DataType< ::rtabmap_msgs::GetPlan > {
  static const char* value()
  {
    return "rtabmap_msgs/GetPlan";
  }

  static const char* value(const ::rtabmap_msgs::GetPlan&) { return value(); }
};


// service_traits::MD5Sum< ::rtabmap_msgs::GetPlanRequest> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GetPlan >
template<>
struct MD5Sum< ::rtabmap_msgs::GetPlanRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GetPlan >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetPlanRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GetPlanRequest> should match
// service_traits::DataType< ::rtabmap_msgs::GetPlan >
template<>
struct DataType< ::rtabmap_msgs::GetPlanRequest>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GetPlan >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetPlanRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rtabmap_msgs::GetPlanResponse> should match
// service_traits::MD5Sum< ::rtabmap_msgs::GetPlan >
template<>
struct MD5Sum< ::rtabmap_msgs::GetPlanResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rtabmap_msgs::GetPlan >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetPlanResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rtabmap_msgs::GetPlanResponse> should match
// service_traits::DataType< ::rtabmap_msgs::GetPlan >
template<>
struct DataType< ::rtabmap_msgs::GetPlanResponse>
{
  static const char* value()
  {
    return DataType< ::rtabmap_msgs::GetPlan >::value();
  }
  static const char* value(const ::rtabmap_msgs::GetPlanResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // RTABMAP_MSGS_MESSAGE_GETPLAN_H
