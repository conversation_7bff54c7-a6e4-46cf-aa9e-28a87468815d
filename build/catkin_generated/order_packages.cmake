# generated from catkin/cmake/em/order_packages.cmake.em

set(CATKIN_ORDERED_PACKAGES "")
set(CATKIN_ORDERED_PACKAGE_PATHS "")
set(CATKIN_ORDERED_PACKAGES_IS_META "")
set(CATKIN_ORDERED_PACKAGES_BUILD_TYPE "")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_launch")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_launch")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_ros")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_ros")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "turtlebot3_slam_3d")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "turtlebot3_slam_3d")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_python")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_python")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "darknet_ros_msgs")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "darknet_ros/darknet_ros_msgs")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "darknet_ros")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "darknet_ros/darknet_ros")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_msgs")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_msgs")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_conversions")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_conversions")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_demos")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_demos")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_examples")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_examples")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_sync")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_sync")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_util")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_util")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_legacy")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_legacy")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_odom")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_odom")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_slam")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_slam")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_viz")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_viz")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "tsdf_mapping")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "tsdf_mapping")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_rviz_plugins")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_rviz_plugins")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "semantic_perception")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "semantic_perception")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "semantic_mapping")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "semantic_mapping")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "rtabmap_costmap_plugins")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "rtabmap_ros/rtabmap_costmap_plugins")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")

set(CATKIN_MESSAGE_GENERATORS )

set(CATKIN_METAPACKAGE_CMAKE_TEMPLATE "/usr/lib/python3/dist-packages/catkin_pkg/templates/metapackage.cmake.in")
