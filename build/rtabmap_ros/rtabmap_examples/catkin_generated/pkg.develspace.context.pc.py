# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "".split(';') if "" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;rtabmap_conversions;message_filters".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "rtabmap_examples"
PROJECT_SPACE_DIR = "/root/autodl-tmp/rtab_ws/devel"
PROJECT_VERSION = "0.21.13"
