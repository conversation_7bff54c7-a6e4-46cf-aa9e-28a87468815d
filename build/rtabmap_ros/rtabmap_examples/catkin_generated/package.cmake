set(_CATKIN_CURRENT_PACKAGE "rtabmap_examples")
set(rtabmap_examples_VERSION "0.21.13")
set(rtabmap_examples_MAINTAINER "Mathieu Labbe <<EMAIL>>")
set(rtabmap_examples_PACKAGE_FORMAT "2")
set(rtabmap_examples_BUILD_DEPENDS "roscpp" "rtabmap_conversions" "message_filters")
set(rtabmap_examples_BUILD_EXPORT_DEPENDS "roscpp" "rtabmap_conversions" "message_filters")
set(rtabmap_examples_BUILDTOOL_DEPENDS "catkin")
set(rtabmap_examples_BUILDTOOL_EXPORT_DEPENDS )
set(rtabmap_examples_EXEC_DEPENDS "rtabmap_costmap_plugins" "rtabmap_launch" "rtabmap_msgs" "rtabmap_odom" "rtabmap_rviz_plugins" "rtabmap_slam" "rtabmap_util" "rtabmap_viz" "rtabmap_demos" "robot_localization" "imu_filter_madgwick" "imu_complementary_filter" "realsense2_camera" "velodyne_pointcloud" "roscpp" "rtabmap_conversions" "message_filters")
set(rtabmap_examples_RUN_DEPENDS "rtabmap_costmap_plugins" "rtabmap_launch" "rtabmap_msgs" "rtabmap_odom" "rtabmap_rviz_plugins" "rtabmap_slam" "rtabmap_util" "rtabmap_viz" "rtabmap_demos" "robot_localization" "imu_filter_madgwick" "imu_complementary_filter" "realsense2_camera" "velodyne_pointcloud" "roscpp" "rtabmap_conversions" "message_filters")
set(rtabmap_examples_TEST_DEPENDS )
set(rtabmap_examples_DOC_DEPENDS )
set(rtabmap_examples_URL_WEBSITE "")
set(rtabmap_examples_URL_BUGTRACKER "https://github.com/introlab/rtabmap_ros/issues")
set(rtabmap_examples_URL_REPOSITORY "https://github.com/introlab/rtabmap_ros")
set(rtabmap_examples_DEPRECATED "")