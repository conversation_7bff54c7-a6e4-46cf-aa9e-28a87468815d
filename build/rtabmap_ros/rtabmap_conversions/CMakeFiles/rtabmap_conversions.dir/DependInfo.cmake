# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_conversions/src/MsgConversion.cpp" "/root/autodl-tmp/rtab_ws/build/rtabmap_ros/rtabmap_conversions/CMakeFiles/rtabmap_conversions.dir/src/MsgConversion.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "DISABLE_LIBUSB_1_0"
  "DISABLE_PCAP"
  "DISABLE_PNG"
  "QT_CORE_LIB"
  "QT_GUI_LIB"
  "QT_NO_DEBUG"
  "QT_OPENGL_LIB"
  "QT_WIDGETS_LIB"
  "ROSCONSOLE_BACKEND_LOG4CXX"
  "ROS_BUILD_SHARED_LIBS=1"
  "ROS_PACKAGE_NAME=\"rtabmap_conversions\""
  "qh_QHpointer"
  "rtabmap_conversions_EXPORTS"
  "vtkRenderingContext2D_AUTOINIT=1(vtkRenderingContextOpenGL2)"
  "vtkRenderingCore_AUTOINIT=3(vtkInteractionStyle,vtkRenderingFreeType,vtkRenderingOpenGL2)"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_conversions/include"
  "/root/autodl-tmp/rtab_ws/devel/include"
  "/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp"
  "/usr/include/vtk-7.1"
  "/usr/include/freetype2"
  "/opt/ros/noetic/include"
  "/usr/include/opencv4"
  "/usr/include/eigen3"
  "/usr/include/pcl-1.10"
  "/opt/ros/noetic/include/rtabmap-0.21"
  "/usr/include/ni"
  "/usr/include/openni2"
  "/usr/include/x86_64-linux-gnu/qt5"
  "/usr/include/x86_64-linux-gnu/qt5/QtWidgets"
  "/usr/include/x86_64-linux-gnu/qt5/QtGui"
  "/usr/include/x86_64-linux-gnu/qt5/QtCore"
  "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++"
  "/usr/include/x86_64-linux-gnu/qt5/QtOpenGL"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
