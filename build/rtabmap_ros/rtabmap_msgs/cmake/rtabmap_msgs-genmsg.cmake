# generated from genmsg/cmake/pkg-genmsg.cmake.em

message(STATUS "rtabmap_msgs: 23 messages, 16 services")

set(MSG_I_FLAGS "-Irtabmap_msgs:/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg;-Istd_msgs:/opt/ros/noetic/share/std_msgs/cmake/../msg;-Igeometry_msgs:/opt/ros/noetic/share/geometry_msgs/cmake/../msg;-Isensor_msgs:/opt/ros/noetic/share/sensor_msgs/cmake/../msg")

# Find all generators
find_package(gencpp REQUIRED)
find_package(geneus REQUIRED)
find_package(genlisp REQUIRED)
find_package(gennodejs REQUIRED)
find_package(genpy REQUIRED)

add_custom_target(rtabmap_msgs_generate_messages ALL)

# verify that message/service dependencies have not changed since configure



get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg" "geometry_msgs/Point:geometry_msgs/Transform:geometry_msgs/Quaternion:rtabmap_msgs/MapGraph:rtabmap_msgs/Link:geometry_msgs/Vector3:geometry_msgs/Pose:std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg" "rtabmap_msgs/Point2f"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg" "std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg" "rtabmap_msgs/GlobalDescriptor:sensor_msgs/PointField:sensor_msgs/LaserScan:sensor_msgs/PointCloud2:std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg" "rtabmap_msgs/GlobalDescriptor:geometry_msgs/Transform:rtabmap_msgs/EnvSensor:sensor_msgs/Image:rtabmap_msgs/MapGraph:rtabmap_msgs/LandmarkDetection:sensor_msgs/CameraInfo:sensor_msgs/Imu:sensor_msgs/PointField:geometry_msgs/Vector3:rtabmap_msgs/GPS:geometry_msgs/Quaternion:rtabmap_msgs/Link:sensor_msgs/RegionOfInterest:geometry_msgs/Point:geometry_msgs/Pose:geometry_msgs/PoseWithCovariance:rtabmap_msgs/Point2f:rtabmap_msgs/Point3f:rtabmap_msgs/KeyPoint:rtabmap_msgs/SensorData:sensor_msgs/PointCloud2:std_msgs/Header:rtabmap_msgs/Node"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg" "geometry_msgs/Point:geometry_msgs/Transform:geometry_msgs/Quaternion:rtabmap_msgs/Link:geometry_msgs/Vector3:geometry_msgs/Pose:std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg" "rtabmap_msgs/GlobalDescriptor:geometry_msgs/Transform:rtabmap_msgs/EnvSensor:sensor_msgs/Image:rtabmap_msgs/LandmarkDetection:sensor_msgs/CameraInfo:sensor_msgs/Imu:sensor_msgs/PointField:geometry_msgs/Vector3:rtabmap_msgs/GPS:geometry_msgs/Quaternion:sensor_msgs/RegionOfInterest:geometry_msgs/Pose:geometry_msgs/Point:geometry_msgs/PoseWithCovariance:rtabmap_msgs/Point2f:rtabmap_msgs/Point3f:rtabmap_msgs/KeyPoint:rtabmap_msgs/SensorData:sensor_msgs/PointCloud2:std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg" "rtabmap_msgs/GlobalDescriptor:geometry_msgs/Transform:rtabmap_msgs/EnvSensor:sensor_msgs/Image:rtabmap_msgs/LandmarkDetection:sensor_msgs/CameraInfo:sensor_msgs/Imu:sensor_msgs/PointField:geometry_msgs/Vector3:rtabmap_msgs/GPS:geometry_msgs/Quaternion:sensor_msgs/RegionOfInterest:geometry_msgs/Point:geometry_msgs/Pose:geometry_msgs/PoseWithCovariance:rtabmap_msgs/Point3f:rtabmap_msgs/Point2f:rtabmap_msgs/KeyPoint:sensor_msgs/PointCloud2:std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg" "geometry_msgs/Vector3:geometry_msgs/Quaternion:geometry_msgs/Transform"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg" "geometry_msgs/Point:geometry_msgs/Transform:rtabmap_msgs/Point2f:rtabmap_msgs/Point3f:geometry_msgs/Quaternion:rtabmap_msgs/CameraModel:sensor_msgs/PointField:rtabmap_msgs/CameraModels:rtabmap_msgs/KeyPoint:sensor_msgs/PointCloud2:geometry_msgs/Vector3:sensor_msgs/RegionOfInterest:geometry_msgs/Pose:std_msgs/Header:sensor_msgs/CameraInfo"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg" "geometry_msgs/Quaternion:geometry_msgs/Pose:geometry_msgs/Point:geometry_msgs/PoseWithCovariance:std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg" "geometry_msgs/Quaternion:geometry_msgs/Pose:geometry_msgs/Point:geometry_msgs/PoseWithCovariance:std_msgs/Header:rtabmap_msgs/LandmarkDetection"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg" "std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg" "rtabmap_msgs/GlobalDescriptor:rtabmap_msgs/Point2f:rtabmap_msgs/Point3f:sensor_msgs/Image:sensor_msgs/CompressedImage:rtabmap_msgs/KeyPoint:sensor_msgs/RegionOfInterest:std_msgs/Header:sensor_msgs/CameraInfo"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg" "rtabmap_msgs/GlobalDescriptor:rtabmap_msgs/RGBDImage:rtabmap_msgs/Point2f:rtabmap_msgs/Point3f:sensor_msgs/Image:sensor_msgs/CompressedImage:rtabmap_msgs/KeyPoint:sensor_msgs/RegionOfInterest:std_msgs/Header:sensor_msgs/CameraInfo"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg" "std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg" "std_msgs/Header:geometry_msgs/Quaternion:geometry_msgs/Point:geometry_msgs/Pose"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg" "std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg" "geometry_msgs/Transform:geometry_msgs/Quaternion:geometry_msgs/Vector3:sensor_msgs/RegionOfInterest:std_msgs/Header:sensor_msgs/CameraInfo"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg" "geometry_msgs/Transform:geometry_msgs/Quaternion:rtabmap_msgs/CameraModel:geometry_msgs/Vector3:sensor_msgs/RegionOfInterest:std_msgs/Header:sensor_msgs/CameraInfo"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv" "rtabmap_msgs/GlobalDescriptor:geometry_msgs/Transform:rtabmap_msgs/EnvSensor:sensor_msgs/Image:rtabmap_msgs/MapGraph:rtabmap_msgs/LandmarkDetection:sensor_msgs/CameraInfo:sensor_msgs/Imu:sensor_msgs/PointField:geometry_msgs/Vector3:rtabmap_msgs/MapData:rtabmap_msgs/GPS:geometry_msgs/Quaternion:rtabmap_msgs/Link:sensor_msgs/RegionOfInterest:geometry_msgs/Point:geometry_msgs/Pose:geometry_msgs/PoseWithCovariance:rtabmap_msgs/Point2f:rtabmap_msgs/Point3f:rtabmap_msgs/KeyPoint:rtabmap_msgs/SensorData:sensor_msgs/PointCloud2:std_msgs/Header:rtabmap_msgs/Node"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv" "rtabmap_msgs/GlobalDescriptor:geometry_msgs/Transform:rtabmap_msgs/EnvSensor:sensor_msgs/Image:rtabmap_msgs/MapGraph:rtabmap_msgs/LandmarkDetection:sensor_msgs/CameraInfo:sensor_msgs/Imu:sensor_msgs/PointField:geometry_msgs/Vector3:rtabmap_msgs/MapData:rtabmap_msgs/GPS:geometry_msgs/Quaternion:rtabmap_msgs/Link:sensor_msgs/RegionOfInterest:geometry_msgs/Point:geometry_msgs/Pose:geometry_msgs/PoseWithCovariance:rtabmap_msgs/Point2f:rtabmap_msgs/Point3f:rtabmap_msgs/KeyPoint:rtabmap_msgs/SensorData:sensor_msgs/PointCloud2:std_msgs/Header:rtabmap_msgs/Node"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv" "geometry_msgs/Quaternion:geometry_msgs/Point:geometry_msgs/Pose"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv" "rtabmap_msgs/Path:geometry_msgs/Quaternion:geometry_msgs/Pose:geometry_msgs/Point:geometry_msgs/PoseStamped:std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv" "rtabmap_msgs/Link:geometry_msgs/Vector3:geometry_msgs/Quaternion:geometry_msgs/Transform"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv" "rtabmap_msgs/GlobalDescriptor:geometry_msgs/Transform:rtabmap_msgs/EnvSensor:sensor_msgs/Image:rtabmap_msgs/LandmarkDetection:sensor_msgs/CameraInfo:sensor_msgs/Imu:sensor_msgs/PointField:geometry_msgs/Vector3:rtabmap_msgs/GPS:geometry_msgs/Quaternion:sensor_msgs/RegionOfInterest:geometry_msgs/Pose:geometry_msgs/Point:geometry_msgs/PoseWithCovariance:rtabmap_msgs/Point2f:rtabmap_msgs/Point3f:rtabmap_msgs/KeyPoint:rtabmap_msgs/SensorData:sensor_msgs/PointCloud2:std_msgs/Header:rtabmap_msgs/Node"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv" "geometry_msgs/Quaternion:geometry_msgs/Point:geometry_msgs/Pose"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv" NAME_WE)
add_custom_target(_rtabmap_msgs_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "rtabmap_msgs" "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv" ""
)

#
#  langs = gencpp;geneus;genlisp;gennodejs;genpy
#

### Section generating for lang: gencpp
### Generating Messages
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/LaserScan.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)

### Generating Services
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_cpp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
)

### Generating Module File
_generate_module_cpp(rtabmap_msgs
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
  "${ALL_GEN_OUTPUT_FILES_cpp}"
)

add_custom_target(rtabmap_msgs_generate_messages_cpp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_cpp}
)
add_dependencies(rtabmap_msgs_generate_messages rtabmap_msgs_generate_messages_cpp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_cpp _rtabmap_msgs_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(rtabmap_msgs_gencpp)
add_dependencies(rtabmap_msgs_gencpp rtabmap_msgs_generate_messages_cpp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS rtabmap_msgs_generate_messages_cpp)

### Section generating for lang: geneus
### Generating Messages
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/LaserScan.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)

### Generating Services
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_eus(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
)

### Generating Module File
_generate_module_eus(rtabmap_msgs
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
  "${ALL_GEN_OUTPUT_FILES_eus}"
)

add_custom_target(rtabmap_msgs_generate_messages_eus
  DEPENDS ${ALL_GEN_OUTPUT_FILES_eus}
)
add_dependencies(rtabmap_msgs_generate_messages rtabmap_msgs_generate_messages_eus)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_eus _rtabmap_msgs_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(rtabmap_msgs_geneus)
add_dependencies(rtabmap_msgs_geneus rtabmap_msgs_generate_messages_eus)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS rtabmap_msgs_generate_messages_eus)

### Section generating for lang: genlisp
### Generating Messages
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/LaserScan.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)

### Generating Services
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_lisp(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
)

### Generating Module File
_generate_module_lisp(rtabmap_msgs
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
  "${ALL_GEN_OUTPUT_FILES_lisp}"
)

add_custom_target(rtabmap_msgs_generate_messages_lisp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_lisp}
)
add_dependencies(rtabmap_msgs_generate_messages rtabmap_msgs_generate_messages_lisp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_lisp _rtabmap_msgs_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(rtabmap_msgs_genlisp)
add_dependencies(rtabmap_msgs_genlisp rtabmap_msgs_generate_messages_lisp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS rtabmap_msgs_generate_messages_lisp)

### Section generating for lang: gennodejs
### Generating Messages
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/LaserScan.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)

### Generating Services
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_nodejs(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
)

### Generating Module File
_generate_module_nodejs(rtabmap_msgs
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
  "${ALL_GEN_OUTPUT_FILES_nodejs}"
)

add_custom_target(rtabmap_msgs_generate_messages_nodejs
  DEPENDS ${ALL_GEN_OUTPUT_FILES_nodejs}
)
add_dependencies(rtabmap_msgs_generate_messages rtabmap_msgs_generate_messages_nodejs)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_nodejs _rtabmap_msgs_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(rtabmap_msgs_gennodejs)
add_dependencies(rtabmap_msgs_gennodejs rtabmap_msgs_generate_messages_nodejs)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS rtabmap_msgs_generate_messages_nodejs)

### Section generating for lang: genpy
### Generating Messages
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/LaserScan.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CompressedImage.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_msg_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)

### Generating Services
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv"
  "${MSG_I_FLAGS}"
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Transform.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Imu.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointField.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseWithCovariance.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/PointCloud2.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)
_generate_srv_py(rtabmap_msgs
  "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
)

### Generating Module File
_generate_module_py(rtabmap_msgs
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
  "${ALL_GEN_OUTPUT_FILES_py}"
)

add_custom_target(rtabmap_msgs_generate_messages_py
  DEPENDS ${ALL_GEN_OUTPUT_FILES_py}
)
add_dependencies(rtabmap_msgs_generate_messages rtabmap_msgs_generate_messages_py)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Info.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/KeyPoint.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GlobalDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/ScanDescriptor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/MapGraph.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Node.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/SensorData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Link.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/OdomInfo.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetection.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/LandmarkDetections.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point2f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Point3f.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Goal.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImage.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/RGBDImages.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/UserData.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/GPS.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/Path.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/EnvSensor.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModel.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/msg/CameraModels.msg" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetMap2.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ListLabels.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/PublishMap.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/ResetPose.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetGoal.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/SetLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/RemoveLabel.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetPlan.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/AddLink.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodeData.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GetNodesInRadius.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/LoadDatabase.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/DetectMoreLoopClosures.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/GlobalBundleAdjustment.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/rtabmap_ros/rtabmap_msgs/srv/CleanupLocalGrids.srv" NAME_WE)
add_dependencies(rtabmap_msgs_generate_messages_py _rtabmap_msgs_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(rtabmap_msgs_genpy)
add_dependencies(rtabmap_msgs_genpy rtabmap_msgs_generate_messages_py)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS rtabmap_msgs_generate_messages_py)



if(gencpp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/rtabmap_msgs
    DESTINATION ${gencpp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_cpp)
  add_dependencies(rtabmap_msgs_generate_messages_cpp std_msgs_generate_messages_cpp)
endif()
if(TARGET geometry_msgs_generate_messages_cpp)
  add_dependencies(rtabmap_msgs_generate_messages_cpp geometry_msgs_generate_messages_cpp)
endif()
if(TARGET sensor_msgs_generate_messages_cpp)
  add_dependencies(rtabmap_msgs_generate_messages_cpp sensor_msgs_generate_messages_cpp)
endif()

if(geneus_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/rtabmap_msgs
    DESTINATION ${geneus_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_eus)
  add_dependencies(rtabmap_msgs_generate_messages_eus std_msgs_generate_messages_eus)
endif()
if(TARGET geometry_msgs_generate_messages_eus)
  add_dependencies(rtabmap_msgs_generate_messages_eus geometry_msgs_generate_messages_eus)
endif()
if(TARGET sensor_msgs_generate_messages_eus)
  add_dependencies(rtabmap_msgs_generate_messages_eus sensor_msgs_generate_messages_eus)
endif()

if(genlisp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/rtabmap_msgs
    DESTINATION ${genlisp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_lisp)
  add_dependencies(rtabmap_msgs_generate_messages_lisp std_msgs_generate_messages_lisp)
endif()
if(TARGET geometry_msgs_generate_messages_lisp)
  add_dependencies(rtabmap_msgs_generate_messages_lisp geometry_msgs_generate_messages_lisp)
endif()
if(TARGET sensor_msgs_generate_messages_lisp)
  add_dependencies(rtabmap_msgs_generate_messages_lisp sensor_msgs_generate_messages_lisp)
endif()

if(gennodejs_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/rtabmap_msgs
    DESTINATION ${gennodejs_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_nodejs)
  add_dependencies(rtabmap_msgs_generate_messages_nodejs std_msgs_generate_messages_nodejs)
endif()
if(TARGET geometry_msgs_generate_messages_nodejs)
  add_dependencies(rtabmap_msgs_generate_messages_nodejs geometry_msgs_generate_messages_nodejs)
endif()
if(TARGET sensor_msgs_generate_messages_nodejs)
  add_dependencies(rtabmap_msgs_generate_messages_nodejs sensor_msgs_generate_messages_nodejs)
endif()

if(genpy_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs)
  install(CODE "execute_process(COMMAND \"/root/miniconda3/bin/python3\" -m compileall \"${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs\")")
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/rtabmap_msgs
    DESTINATION ${genpy_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_py)
  add_dependencies(rtabmap_msgs_generate_messages_py std_msgs_generate_messages_py)
endif()
if(TARGET geometry_msgs_generate_messages_py)
  add_dependencies(rtabmap_msgs_generate_messages_py geometry_msgs_generate_messages_py)
endif()
if(TARGET sensor_msgs_generate_messages_py)
  add_dependencies(rtabmap_msgs_generate_messages_py sensor_msgs_generate_messages_py)
endif()
