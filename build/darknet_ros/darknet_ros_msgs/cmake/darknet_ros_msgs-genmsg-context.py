# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros_msgs/msg/BoundingBox.msg;/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros_msgs/msg/BoundingBoxes.msg;/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros_msgs/msg/ObjectCount.msg;/root/autodl-tmp/rtab_ws/devel/share/darknet_ros_msgs/msg/CheckForObjectsAction.msg;/root/autodl-tmp/rtab_ws/devel/share/darknet_ros_msgs/msg/CheckForObjectsActionGoal.msg;/root/autodl-tmp/rtab_ws/devel/share/darknet_ros_msgs/msg/CheckForObjectsActionResult.msg;/root/autodl-tmp/rtab_ws/devel/share/darknet_ros_msgs/msg/CheckForObjectsActionFeedback.msg;/root/autodl-tmp/rtab_ws/devel/share/darknet_ros_msgs/msg/CheckForObjectsGoal.msg;/root/autodl-tmp/rtab_ws/devel/share/darknet_ros_msgs/msg/CheckForObjectsResult.msg;/root/autodl-tmp/rtab_ws/devel/share/darknet_ros_msgs/msg/CheckForObjectsFeedback.msg"
services_str = ""
pkg_name = "darknet_ros_msgs"
dependencies_str = "actionlib_msgs;geometry_msgs;sensor_msgs;std_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "darknet_ros_msgs;/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros_msgs/msg;darknet_ros_msgs;/root/autodl-tmp/rtab_ws/devel/share/darknet_ros_msgs/msg;actionlib_msgs;/opt/ros/noetic/share/actionlib_msgs/cmake/../msg;geometry_msgs;/opt/ros/noetic/share/geometry_msgs/cmake/../msg;sensor_msgs;/opt/ros/noetic/share/sensor_msgs/cmake/../msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/root/miniconda3/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
