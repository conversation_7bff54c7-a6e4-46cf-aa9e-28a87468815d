set(_CATKIN_CURRENT_PACKAGE "darknet_ros_msgs")
set(darknet_ros_msgs_VERSION "1.1.5")
set(darknet_ros_msgs_MAINTAINER "<PERSON><PERSON> <<EMAIL>>")
set(darknet_ros_msgs_PACKAGE_FORMAT "1")
set(darknet_ros_msgs_BUILD_DEPENDS "actionlib_msgs" "geometry_msgs" "sensor_msgs" "message_generation" "std_msgs")
set(darknet_ros_msgs_BUILD_EXPORT_DEPENDS "actionlib_msgs" "geometry_msgs" "sensor_msgs" "message_runtime" "std_msgs")
set(darknet_ros_msgs_BUILDTOOL_DEPENDS "catkin")
set(darknet_ros_msgs_BUILDTOOL_EXPORT_DEPENDS )
set(darknet_ros_msgs_EXEC_DEPENDS "actionlib_msgs" "geometry_msgs" "sensor_msgs" "message_runtime" "std_msgs")
set(darknet_ros_msgs_RUN_DEPENDS "actionlib_msgs" "geometry_msgs" "sensor_msgs" "message_runtime" "std_msgs")
set(darknet_ros_msgs_TEST_DEPENDS )
set(darknet_ros_msgs_DOC_DEPENDS )
set(darknet_ros_msgs_URL_WEBSITE "https://github.com/leggedrobotics/darknet_ros")
set(darknet_ros_msgs_URL_BUGTRACKER "")
set(darknet_ros_msgs_URL_REPOSITORY "")
set(darknet_ros_msgs_DEPRECATED "")