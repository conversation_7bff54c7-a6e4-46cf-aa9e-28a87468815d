# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros/include;/usr/include".split(';') if "/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros/include;/usr/include" != "" else []
PROJECT_CATKIN_DEPENDS = "cv_bridge;roscpp;actionlib;rospy;std_msgs;darknet_ros_msgs;image_transport;nodelet".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-ldarknet_ros_lib;/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0".split(';') if "-ldarknet_ros_lib;/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0" != "" else []
PROJECT_NAME = "darknet_ros"
PROJECT_SPACE_DIR = "/root/autodl-tmp/rtab_ws/devel"
PROJECT_VERSION = "1.1.5"
