<?xml version="1.0"?>
<package format="2">
  <name>darknet_ros</name>
  <version>1.1.5</version>
  <description>Darknet is an open source neural network framework that runs on CPU and GPU. You only look once (YOLO) is a state-of-the-art, real-time object detection system.</description>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <license>BSD</license>
  <url type="website">https://github.com/leggedrobotics/darknet_ros</url>
  <author email="<EMAIL>"><PERSON><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>
  <depend>boost</depend>
  <depend>libopencv-dev</depend>
  <depend>libx11</depend>
  <depend>libxt-dev</depend>
  <depend>libxext</depend>

  <depend>roscpp</depend>
  <depend>rospy</depend>
  <depend>std_msgs</depend>
  <depend>image_transport</depend>
  <depend>cv_bridge</depend>
  <depend>sensor_msgs</depend>
  <depend>message_generation</depend>
  <depend>darknet_ros_msgs</depend>
  <depend>actionlib</depend>
  <depend>nodelet</depend>

  <!-- Test dependencies -->
  <test_depend>rostest</test_depend>
  <test_depend>wget</test_depend>

  <!-- Export nodelet plugins -->
  <export>
    <nodelet plugin="${prefix}/nodelet_plugins.xml" />
  </export>
</package>
