set(_CATKIN_CURRENT_PACKAGE "darknet_ros")
set(darknet_ros_VERSION "1.1.5")
set(darknet_ros_MAINTAINER "<PERSON><PERSON> Bjelonic <<EMAIL>>")
set(darknet_ros_PACKAGE_FORMAT "2")
set(darknet_ros_BUILD_DEPENDS "boost" "libopencv-dev" "libx11" "libxt-dev" "libxext" "roscpp" "rospy" "std_msgs" "image_transport" "cv_bridge" "sensor_msgs" "message_generation" "darknet_ros_msgs" "actionlib" "nodelet")
set(darknet_ros_BUILD_EXPORT_DEPENDS "boost" "libopencv-dev" "libx11" "libxt-dev" "libxext" "roscpp" "rospy" "std_msgs" "image_transport" "cv_bridge" "sensor_msgs" "message_generation" "darknet_ros_msgs" "actionlib" "nodelet")
set(darknet_ros_BUILDTOOL_DEPENDS "catkin")
set(darknet_ros_BUILDTOOL_EXPORT_DEPENDS )
set(darknet_ros_EXEC_DEPENDS "boost" "libopencv-dev" "libx11" "libxt-dev" "libxext" "roscpp" "rospy" "std_msgs" "image_transport" "cv_bridge" "sensor_msgs" "message_generation" "darknet_ros_msgs" "actionlib" "nodelet")
set(darknet_ros_RUN_DEPENDS "boost" "libopencv-dev" "libx11" "libxt-dev" "libxext" "roscpp" "rospy" "std_msgs" "image_transport" "cv_bridge" "sensor_msgs" "message_generation" "darknet_ros_msgs" "actionlib" "nodelet")
set(darknet_ros_TEST_DEPENDS "rostest" "wget")
set(darknet_ros_DOC_DEPENDS )
set(darknet_ros_URL_WEBSITE "https://github.com/leggedrobotics/darknet_ros")
set(darknet_ros_URL_BUGTRACKER "")
set(darknet_ros_URL_REPOSITORY "")
set(darknet_ros_DEPRECATED "")