# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/autodl-tmp/rtab_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/autodl-tmp/rtab_ws/build

# Utility rule file for _run_tests_darknet_ros_rostest_test_object_detection.test.

# Include the progress variables for this target.
include darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/progress.make

darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test:
	cd /root/autodl-tmp/rtab_ws/build/darknet_ros/darknet_ros && ../../catkin_generated/env_cached.sh /root/miniconda3/bin/python3 /opt/ros/noetic/share/catkin/cmake/test/run_tests.py /root/autodl-tmp/rtab_ws/build/test_results/darknet_ros/rostest-test_object_detection.xml "/root/miniconda3/bin/python3 /opt/ros/noetic/share/rostest/cmake/../../../bin/rostest --pkgdir=/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros --package=darknet_ros --results-filename test_object_detection.xml --results-base-dir \"/root/autodl-tmp/rtab_ws/build/test_results\" /root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros/test/object_detection.test "

_run_tests_darknet_ros_rostest_test_object_detection.test: darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test
_run_tests_darknet_ros_rostest_test_object_detection.test: darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/build.make

.PHONY : _run_tests_darknet_ros_rostest_test_object_detection.test

# Rule to build all files generated by this target.
darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/build: _run_tests_darknet_ros_rostest_test_object_detection.test

.PHONY : darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/build

darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/clean:
	cd /root/autodl-tmp/rtab_ws/build/darknet_ros/darknet_ros && $(CMAKE_COMMAND) -P CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/cmake_clean.cmake
.PHONY : darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/clean

darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/depend:
	cd /root/autodl-tmp/rtab_ws/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/autodl-tmp/rtab_ws/src /root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros /root/autodl-tmp/rtab_ws/build /root/autodl-tmp/rtab_ws/build/darknet_ros/darknet_ros /root/autodl-tmp/rtab_ws/build/darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : darknet_ros/darknet_ros/CMakeFiles/_run_tests_darknet_ros_rostest_test_object_detection.test.dir/depend

