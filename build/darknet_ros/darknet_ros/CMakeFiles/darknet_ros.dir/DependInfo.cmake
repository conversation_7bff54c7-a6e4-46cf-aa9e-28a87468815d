# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros/src/yolo_object_detector_node.cpp" "/root/autodl-tmp/rtab_ws/build/darknet_ros/darknet_ros/CMakeFiles/darknet_ros.dir/src/yolo_object_detector_node.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "BOOST_ALL_NO_LIB"
  "BOOST_ATOMIC_DYN_LINK"
  "BOOST_THREAD_DYN_LINK"
  "DARKNET_FILE_PATH=\"/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet\""
  "GPU"
  "OPENCV"
  "ROSCONSOLE_BACKEND_LOG4CXX"
  "ROS_BUILD_SHARED_LIBS=1"
  "ROS_PACKAGE_NAME=\"darknet_ros\""
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet/src"
  "/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet/include"
  "/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros/include"
  "/root/autodl-tmp/rtab_ws/devel/include"
  "/opt/ros/noetic/include"
  "/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp"
  "/usr/local/cuda/include"
  "/usr/include/opencv4"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/root/autodl-tmp/rtab_ws/build/darknet_ros/darknet_ros/CMakeFiles/darknet_ros_lib.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
