file(REMOVE_RECURSE
  "/root/autodl-tmp/rtab_ws/devel/lib/libdarknet_ros_lib.pdb"
  "/root/autodl-tmp/rtab_ws/devel/lib/libdarknet_ros_lib.so"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/art.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/captcha.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/cifar.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/classifier.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/coco.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/darknet.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/detector.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/go.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/instance-segmenter.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/lsd.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/nightmare.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/regressor.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/rnn.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/segmenter.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/super.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/tag.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/yolo.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/activation_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/activations.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/avgpool_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/batchnorm_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/blas.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/box.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/col2im.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/connected_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/convolutional_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/cost_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/crnn_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/crop_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/cuda.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_activation_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_avgpool_layer_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_blas_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_col2im_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_convolutional_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_crop_layer_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_deconvolutional_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_dropout_layer_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_im2col_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_maxpool_layer_kernels.cu.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/data.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/deconvolutional_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/demo.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/detection_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/dropout_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/gemm.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/gru_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/im2col.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/image.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/image_opencv.cpp.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/iseg_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/l2norm_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/list.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/local_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/logistic_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/lstm_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/matrix.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/maxpool_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/network.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/normalization_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/option_list.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/parser.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/region_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/reorg_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/rnn_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/route_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/shortcut_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/softmax_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/tree.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/upsample_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/utils.c.o"
  "CMakeFiles/darknet_ros_lib.dir/__/darknet/src/yolo_layer.c.o"
  "CMakeFiles/darknet_ros_lib.dir/src/YoloObjectDetector.cpp.o"
  "CMakeFiles/darknet_ros_lib.dir/src/image_interface.cpp.o"
)

# Per-language clean rules from dependency scanning.
foreach(lang C CXX)
  include(CMakeFiles/darknet_ros_lib.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
