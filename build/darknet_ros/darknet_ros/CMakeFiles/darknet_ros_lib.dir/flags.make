# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile C with /usr/bin/cc
# compile CXX with /usr/bin/c++
C_FLAGS = -Wall -Wno-unused-result -Wno-unknown-pragmas -Wno-unused-variable -Wfatal-errors -fPIC  -fPIC   -O4 -g

C_DEFINES = -DBOOST_ALL_NO_LIB -DBOOST_ATOMIC_DYN_LINK -DBOOST_THREAD_DYN_LINK -DDARKNET_FILE_PATH=\"/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet\" -DGPU -DOPENCV -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"darknet_ros\" -Ddarknet_ros_lib_EXPORTS

C_INCLUDES = -I/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet/src -I/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet/include -I/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros/include -I/root/autodl-tmp/rtab_ws/devel/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -I/usr/local/cuda/include -isystem /usr/include/opencv4 

CXX_FLAGS = -fPIC   -O4 -g -std=gnu++11

CXX_DEFINES = -DBOOST_ALL_NO_LIB -DBOOST_ATOMIC_DYN_LINK -DBOOST_THREAD_DYN_LINK -DDARKNET_FILE_PATH=\"/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet\" -DGPU -DOPENCV -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"darknet_ros\" -Ddarknet_ros_lib_EXPORTS

CXX_INCLUDES = -I/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet/src -I/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet/include -I/root/autodl-tmp/rtab_ws/src/darknet_ros/darknet_ros/include -I/root/autodl-tmp/rtab_ws/devel/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -I/usr/local/cuda/include -isystem /usr/include/opencv4 

