/usr/bin/c++ -fPIC   -shared -Wl,-soname,libdarknet_ros_lib.so -o /root/autodl-tmp/rtab_ws/devel/lib/libdarknet_ros_lib.so CMakeFiles/darknet_ros_lib.dir/src/YoloObjectDetector.cpp.o CMakeFiles/darknet_ros_lib.dir/src/image_interface.cpp.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/activation_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/im2col.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/activations.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/image.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/avgpool_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/batchnorm_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/list.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/blas.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/local_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/box.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/lstm_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/col2im.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/matrix.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/connected_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/maxpool_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/convolutional_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/network.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/cost_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/normalization_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/crnn_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/option_list.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/crop_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/parser.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/cuda.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/region_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/data.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/reorg_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/deconvolutional_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/rnn_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/demo.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/route_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/detection_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/shortcut_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/dropout_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/softmax_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/gemm.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/tree.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/gru_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/utils.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/upsample_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/logistic_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/l2norm_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/yolo_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/iseg_layer.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/image_opencv.cpp.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/art.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/lsd.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/nightmare.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/instance-segmenter.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/captcha.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/regressor.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/cifar.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/rnn.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/classifier.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/segmenter.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/coco.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/super.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/darknet.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/tag.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/detector.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/yolo.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/examples/go.c.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_activation_kernels.cu.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_crop_layer_kernels.cu.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_avgpool_layer_kernels.cu.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_deconvolutional_kernels.cu.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_blas_kernels.cu.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_dropout_layer_kernels.cu.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_col2im_kernels.cu.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_im2col_kernels.cu.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_convolutional_kernels.cu.o CMakeFiles/darknet_ros_lib.dir/__/darknet/src/darknet_ros_lib_generated_maxpool_layer_kernels.cu.o   -L/usr/local/cuda/lib64  -Wl,-rpath,/usr/local/cuda/lib64:/opt/ros/noetic/lib: /usr/lib/x86_64-linux-gnu/libSM.so /usr/lib/x86_64-linux-gnu/libICE.so /usr/lib/x86_64-linux-gnu/libX11.so /usr/lib/x86_64-linux-gnu/libXext.so /usr/local/cuda/lib64/libcudart_static.a -lpthread -ldl /usr/lib/x86_64-linux-gnu/librt.so -lcuda -lcudart -lcublas -lcurand -lm -lpthread -lstdc++ /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /opt/ros/noetic/lib/libcv_bridge.so /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0 /opt/ros/noetic/lib/libactionlib.so /opt/ros/noetic/lib/libimage_transport.so /opt/ros/noetic/lib/libmessage_filters.so /opt/ros/noetic/lib/libnodeletlib.so /opt/ros/noetic/lib/libbondcpp.so /usr/lib/x86_64-linux-gnu/libuuid.so /opt/ros/noetic/lib/libclass_loader.so /usr/lib/x86_64-linux-gnu/libPocoFoundation.so /usr/lib/x86_64-linux-gnu/libdl.so /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so /usr/lib/x86_64-linux-gnu/libpython3.8.so /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0 /usr/lib/x86_64-linux-gnu/libtinyxml2.so /opt/ros/noetic/lib/libroscpp.so /usr/lib/x86_64-linux-gnu/libpthread.so /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so /usr/lib/x86_64-linux-gnu/liblog4cxx.so /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/librostime.so /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0 /usr/lib/x86_64-linux-gnu/libboost_atomic.so.1.71.0 -lpthread /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0 
