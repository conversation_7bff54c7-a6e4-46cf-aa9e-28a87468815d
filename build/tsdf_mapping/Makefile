# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/autodl-tmp/rtab_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/autodl-tmp/rtab_ws/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# The main all target
all: cmake_check_build_system
	cd /root/autodl-tmp/rtab_ws/build && $(CMAKE_COMMAND) -E cmake_progress_start /root/autodl-tmp/rtab_ws/build/CMakeFiles /root/autodl-tmp/rtab_ws/build/tsdf_mapping/CMakeFiles/progress.marks
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/autodl-tmp/rtab_ws/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /root/autodl-tmp/rtab_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
tsdf_mapping/CMakeFiles/pose_subscription_center.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/CMakeFiles/pose_subscription_center.dir/rule
.PHONY : tsdf_mapping/CMakeFiles/pose_subscription_center.dir/rule

# Convenience name for target.
pose_subscription_center: tsdf_mapping/CMakeFiles/pose_subscription_center.dir/rule

.PHONY : pose_subscription_center

# fast build rule for target.
pose_subscription_center/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/pose_subscription_center.dir/build.make tsdf_mapping/CMakeFiles/pose_subscription_center.dir/build
.PHONY : pose_subscription_center/fast

# Convenience name for target.
tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/rule
.PHONY : tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/rule

# Convenience name for target.
gpu_monitor_node: tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/rule

.PHONY : gpu_monitor_node

# fast build rule for target.
gpu_monitor_node/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/build.make tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/build
.PHONY : gpu_monitor_node/fast

# Convenience name for target.
tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/rule
.PHONY : tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/rule

# Convenience name for target.
simple_tsdf_fusion_node: tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/rule

.PHONY : simple_tsdf_fusion_node

# fast build rule for target.
simple_tsdf_fusion_node/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/build.make tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/build
.PHONY : simple_tsdf_fusion_node/fast

# Convenience name for target.
tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/rule
.PHONY : tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/rule

# Convenience name for target.
tsdf_fusion_node: tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/rule

.PHONY : tsdf_fusion_node

# fast build rule for target.
tsdf_fusion_node/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/build.make tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/build
.PHONY : tsdf_fusion_node/fast

# Convenience name for target.
tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/rule
.PHONY : tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/rule

# Convenience name for target.
tsdf_mapping_cuda: tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/rule

.PHONY : tsdf_mapping_cuda

# fast build rule for target.
tsdf_mapping_cuda/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/build
.PHONY : tsdf_mapping_cuda/fast

# Convenience name for target.
tsdf_mapping/CMakeFiles/tsdf_mapping.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 tsdf_mapping/CMakeFiles/tsdf_mapping.dir/rule
.PHONY : tsdf_mapping/CMakeFiles/tsdf_mapping.dir/rule

# Convenience name for target.
tsdf_mapping: tsdf_mapping/CMakeFiles/tsdf_mapping.dir/rule

.PHONY : tsdf_mapping

# fast build rule for target.
tsdf_mapping/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping.dir/build
.PHONY : tsdf_mapping/fast

src/gpu_monitor_node.o: src/gpu_monitor_node.cpp.o

.PHONY : src/gpu_monitor_node.o

# target to build an object file
src/gpu_monitor_node.cpp.o:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/build.make tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/src/gpu_monitor_node.cpp.o
.PHONY : src/gpu_monitor_node.cpp.o

src/gpu_monitor_node.i: src/gpu_monitor_node.cpp.i

.PHONY : src/gpu_monitor_node.i

# target to preprocess a source file
src/gpu_monitor_node.cpp.i:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/build.make tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/src/gpu_monitor_node.cpp.i
.PHONY : src/gpu_monitor_node.cpp.i

src/gpu_monitor_node.s: src/gpu_monitor_node.cpp.s

.PHONY : src/gpu_monitor_node.s

# target to generate assembly for a file
src/gpu_monitor_node.cpp.s:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/build.make tsdf_mapping/CMakeFiles/gpu_monitor_node.dir/src/gpu_monitor_node.cpp.s
.PHONY : src/gpu_monitor_node.cpp.s

src/pose_subscription_center.o: src/pose_subscription_center.cpp.o

.PHONY : src/pose_subscription_center.o

# target to build an object file
src/pose_subscription_center.cpp.o:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/pose_subscription_center.dir/build.make tsdf_mapping/CMakeFiles/pose_subscription_center.dir/src/pose_subscription_center.cpp.o
.PHONY : src/pose_subscription_center.cpp.o

src/pose_subscription_center.i: src/pose_subscription_center.cpp.i

.PHONY : src/pose_subscription_center.i

# target to preprocess a source file
src/pose_subscription_center.cpp.i:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/pose_subscription_center.dir/build.make tsdf_mapping/CMakeFiles/pose_subscription_center.dir/src/pose_subscription_center.cpp.i
.PHONY : src/pose_subscription_center.cpp.i

src/pose_subscription_center.s: src/pose_subscription_center.cpp.s

.PHONY : src/pose_subscription_center.s

# target to generate assembly for a file
src/pose_subscription_center.cpp.s:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/pose_subscription_center.dir/build.make tsdf_mapping/CMakeFiles/pose_subscription_center.dir/src/pose_subscription_center.cpp.s
.PHONY : src/pose_subscription_center.cpp.s

src/simple_tsdf_fusion.o: src/simple_tsdf_fusion.cpp.o

.PHONY : src/simple_tsdf_fusion.o

# target to build an object file
src/simple_tsdf_fusion.cpp.o:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/build.make tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/src/simple_tsdf_fusion.cpp.o
.PHONY : src/simple_tsdf_fusion.cpp.o

src/simple_tsdf_fusion.i: src/simple_tsdf_fusion.cpp.i

.PHONY : src/simple_tsdf_fusion.i

# target to preprocess a source file
src/simple_tsdf_fusion.cpp.i:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/build.make tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/src/simple_tsdf_fusion.cpp.i
.PHONY : src/simple_tsdf_fusion.cpp.i

src/simple_tsdf_fusion.s: src/simple_tsdf_fusion.cpp.s

.PHONY : src/simple_tsdf_fusion.s

# target to generate assembly for a file
src/simple_tsdf_fusion.cpp.s:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/build.make tsdf_mapping/CMakeFiles/simple_tsdf_fusion_node.dir/src/simple_tsdf_fusion.cpp.s
.PHONY : src/simple_tsdf_fusion.cpp.s

src/tsdf_cuda.o: src/tsdf_cuda.cu.o

.PHONY : src/tsdf_cuda.o

# target to build an object file
src/tsdf_cuda.cu.o:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/src/tsdf_cuda.cu.o
.PHONY : src/tsdf_cuda.cu.o

src/tsdf_cuda.i: src/tsdf_cuda.cu.i

.PHONY : src/tsdf_cuda.i

# target to preprocess a source file
src/tsdf_cuda.cu.i:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/src/tsdf_cuda.cu.i
.PHONY : src/tsdf_cuda.cu.i

src/tsdf_cuda.s: src/tsdf_cuda.cu.s

.PHONY : src/tsdf_cuda.s

# target to generate assembly for a file
src/tsdf_cuda.cu.s:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping_cuda.dir/src/tsdf_cuda.cu.s
.PHONY : src/tsdf_cuda.cu.s

src/tsdf_fusion.o: src/tsdf_fusion.cpp.o

.PHONY : src/tsdf_fusion.o

# target to build an object file
src/tsdf_fusion.cpp.o:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping.dir/src/tsdf_fusion.cpp.o
.PHONY : src/tsdf_fusion.cpp.o

src/tsdf_fusion.i: src/tsdf_fusion.cpp.i

.PHONY : src/tsdf_fusion.i

# target to preprocess a source file
src/tsdf_fusion.cpp.i:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping.dir/src/tsdf_fusion.cpp.i
.PHONY : src/tsdf_fusion.cpp.i

src/tsdf_fusion.s: src/tsdf_fusion.cpp.s

.PHONY : src/tsdf_fusion.s

# target to generate assembly for a file
src/tsdf_fusion.cpp.s:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping.dir/src/tsdf_fusion.cpp.s
.PHONY : src/tsdf_fusion.cpp.s

src/tsdf_fusion_node.o: src/tsdf_fusion_node.cpp.o

.PHONY : src/tsdf_fusion_node.o

# target to build an object file
src/tsdf_fusion_node.cpp.o:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/build.make tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/src/tsdf_fusion_node.cpp.o
.PHONY : src/tsdf_fusion_node.cpp.o

src/tsdf_fusion_node.i: src/tsdf_fusion_node.cpp.i

.PHONY : src/tsdf_fusion_node.i

# target to preprocess a source file
src/tsdf_fusion_node.cpp.i:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/build.make tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/src/tsdf_fusion_node.cpp.i
.PHONY : src/tsdf_fusion_node.cpp.i

src/tsdf_fusion_node.s: src/tsdf_fusion_node.cpp.s

.PHONY : src/tsdf_fusion_node.s

# target to generate assembly for a file
src/tsdf_fusion_node.cpp.s:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/build.make tsdf_mapping/CMakeFiles/tsdf_fusion_node.dir/src/tsdf_fusion_node.cpp.s
.PHONY : src/tsdf_fusion_node.cpp.s

src/voxblox_adapter.o: src/voxblox_adapter.cpp.o

.PHONY : src/voxblox_adapter.o

# target to build an object file
src/voxblox_adapter.cpp.o:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping.dir/src/voxblox_adapter.cpp.o
.PHONY : src/voxblox_adapter.cpp.o

src/voxblox_adapter.i: src/voxblox_adapter.cpp.i

.PHONY : src/voxblox_adapter.i

# target to preprocess a source file
src/voxblox_adapter.cpp.i:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping.dir/src/voxblox_adapter.cpp.i
.PHONY : src/voxblox_adapter.cpp.i

src/voxblox_adapter.s: src/voxblox_adapter.cpp.s

.PHONY : src/voxblox_adapter.s

# target to generate assembly for a file
src/voxblox_adapter.cpp.s:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f tsdf_mapping/CMakeFiles/tsdf_mapping.dir/build.make tsdf_mapping/CMakeFiles/tsdf_mapping.dir/src/voxblox_adapter.cpp.s
.PHONY : src/voxblox_adapter.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... pose_subscription_center"
	@echo "... install/strip"
	@echo "... gpu_monitor_node"
	@echo "... simple_tsdf_fusion_node"
	@echo "... tsdf_fusion_node"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... tsdf_mapping_cuda"
	@echo "... install/local"
	@echo "... tsdf_mapping"
	@echo "... src/gpu_monitor_node.o"
	@echo "... src/gpu_monitor_node.i"
	@echo "... src/gpu_monitor_node.s"
	@echo "... src/pose_subscription_center.o"
	@echo "... src/pose_subscription_center.i"
	@echo "... src/pose_subscription_center.s"
	@echo "... src/simple_tsdf_fusion.o"
	@echo "... src/simple_tsdf_fusion.i"
	@echo "... src/simple_tsdf_fusion.s"
	@echo "... src/tsdf_cuda.o"
	@echo "... src/tsdf_cuda.i"
	@echo "... src/tsdf_cuda.s"
	@echo "... src/tsdf_fusion.o"
	@echo "... src/tsdf_fusion.i"
	@echo "... src/tsdf_fusion.s"
	@echo "... src/tsdf_fusion_node.o"
	@echo "... src/tsdf_fusion_node.i"
	@echo "... src/tsdf_fusion_node.s"
	@echo "... src/voxblox_adapter.o"
	@echo "... src/voxblox_adapter.i"
	@echo "... src/voxblox_adapter.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /root/autodl-tmp/rtab_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

