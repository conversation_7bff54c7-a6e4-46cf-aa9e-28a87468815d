<?xml version="1.0"?>
<package format="2">
  <name>rtabmap_examples</name>
  <version>0.21.13</version>
  <description>RTAB-Map's example launch files.</description>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <author><PERSON><PERSON></author>
  <license>BSD</license>
  <url type="bugtracker">https://github.com/introlab/rtabmap_ros/issues</url>
  <url type="repository">https://github.com/introlab/rtabmap_ros</url>

  <buildtool_depend>catkin</buildtool_depend>

  <depend>roscpp</depend>
  <depend>rtabmap_conversions</depend>
  <depend>message_filters</depend>

  <exec_depend>rtabmap_costmap_plugins</exec_depend>
  <exec_depend>rtabmap_launch</exec_depend>
  <exec_depend>rtabmap_msgs</exec_depend>
  <exec_depend>rtabmap_odom</exec_depend>
  <exec_depend>rtabmap_rviz_plugins</exec_depend>
  <exec_depend>rtabmap_slam</exec_depend>
  <exec_depend>rtabmap_util</exec_depend>
  <exec_depend>rtabmap_viz</exec_depend>
  <exec_depend>rtabmap_demos</exec_depend>
  
  <exec_depend>robot_localization</exec_depend>
  <exec_depend>imu_filter_madgwick</exec_depend>
  <exec_depend>imu_complementary_filter</exec_depend>
  <exec_depend>realsense2_camera</exec_depend>
  <exec_depend>velodyne_pointcloud</exec_depend>

</package>
