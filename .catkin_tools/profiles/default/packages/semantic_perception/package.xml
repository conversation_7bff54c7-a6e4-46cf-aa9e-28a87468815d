<?xml version="1.0"?>
<package format="2">
  <name>semantic_perception</name>
  <version>0.1.0</version>
  <description>语义感知包，集成多种深度学习模型进行语义分割和目标检测</description>

  <maintainer email="<EMAIL>">RTAB-Map User</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <!-- ROS核心依赖 -->
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>

  <!-- 视觉处理依赖 -->
  <build_depend>cv_bridge</build_depend>
  <build_depend>image_transport</build_depend>
  <build_depend>image_geometry</build_depend>
  <build_depend>vision_msgs</build_depend>

  <!-- 变换和时间 -->
  <build_depend>tf2</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>

  <!-- 动态配置 -->
  <build_depend>dynamic_reconfigure</build_depend>

  <!-- 消息生成 -->
  <build_depend>message_generation</build_depend>

  <!-- 运行时依赖 -->
  <exec_depend>roscpp</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>
  <exec_depend>cv_bridge</exec_depend>
  <exec_depend>image_transport</exec_depend>
  <exec_depend>image_geometry</exec_depend>
  <exec_depend>vision_msgs</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>tf2_geometry_msgs</exec_depend>
  <exec_depend>dynamic_reconfigure</exec_depend>
  <exec_depend>message_runtime</exec_depend>

  <export>
  </export>
</package>
