<?xml version="1.0"?>
<package format="2">
  <name>tsdf_mapping</name>
  <version>0.1.0</version>
  <description>TSDF稠密建图包，用于生成高质量的3D稠密地图</description>

  <maintainer email="<EMAIL>">RTAB-Map User</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <!-- ROS核心依赖 -->
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_ros</build_depend>

  <!-- 点云和图像处理依赖 -->
  <build_depend>pcl_ros</build_depend>
  <build_depend>pcl_conversions</build_depend>
  <build_depend>cv_bridge</build_depend>
  <build_depend>image_transport</build_depend>
  <build_depend>image_geometry</build_depend>

  <!-- 地图相关依赖 -->
  <build_depend>grid_map_ros</build_depend>
  <build_depend>grid_map_core</build_depend>
  <build_depend>grid_map_msgs</build_depend>
  <build_depend>octomap_ros</build_depend>
  <build_depend>octomap_msgs</build_depend>

  <!-- 动态配置 -->
  <build_depend>dynamic_reconfigure</build_depend>

  <!-- 运行时依赖 -->
  <exec_depend>roscpp</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>pcl_ros</exec_depend>
  <exec_depend>pcl_conversions</exec_depend>
  <exec_depend>cv_bridge</exec_depend>
  <exec_depend>image_transport</exec_depend>
  <exec_depend>image_geometry</exec_depend>
  <exec_depend>grid_map_ros</exec_depend>
  <exec_depend>grid_map_core</exec_depend>
  <exec_depend>grid_map_msgs</exec_depend>
  <exec_depend>octomap_ros</exec_depend>
  <exec_depend>octomap_msgs</exec_depend>
  <exec_depend>dynamic_reconfigure</exec_depend>

  <export>
  </export>
</package>
