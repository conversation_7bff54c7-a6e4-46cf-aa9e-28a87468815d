<?xml version="1.0"?>
<package format="2">
  <name>semantic_mapping</name>
  <version>0.1.0</version>
  <description>语义地图构建包，融合TSDF稠密建图和语义信息生成语义地图</description>

  <maintainer email="<EMAIL>">RTAB-Map User</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <!-- ROS核心依赖 -->
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>

  <!-- 地图相关依赖 -->
  <build_depend>grid_map_ros</build_depend>
  <build_depend>grid_map_core</build_depend>
  <build_depend>grid_map_msgs</build_depend>
  <build_depend>octomap_ros</build_depend>
  <build_depend>octomap_msgs</build_depend>

  <!-- 点云处理 -->
  <build_depend>pcl_ros</build_depend>
  <build_depend>pcl_conversions</build_depend>

  <!-- 变换 -->
  <build_depend>tf2</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>

  <!-- 视觉消息 -->
  <build_depend>vision_msgs</build_depend>

  <!-- 自定义包依赖 -->
  <build_depend>tsdf_mapping</build_depend>
  <build_depend>semantic_perception</build_depend>

  <!-- 消息生成 -->
  <build_depend>message_generation</build_depend>

  <!-- 动态配置 -->
  <build_depend>dynamic_reconfigure</build_depend>

  <!-- 运行时依赖 -->
  <exec_depend>roscpp</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>
  <exec_depend>grid_map_ros</exec_depend>
  <exec_depend>grid_map_core</exec_depend>
  <exec_depend>grid_map_msgs</exec_depend>
  <exec_depend>octomap_ros</exec_depend>
  <exec_depend>octomap_msgs</exec_depend>
  <exec_depend>pcl_ros</exec_depend>
  <exec_depend>pcl_conversions</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>tf2_geometry_msgs</exec_depend>
  <exec_depend>vision_msgs</exec_depend>
  <exec_depend>tsdf_mapping</exec_depend>
  <exec_depend>semantic_perception</exec_depend>
  <exec_depend>message_runtime</exec_depend>
  <exec_depend>dynamic_reconfigure</exec_depend>

  <export>
  </export>
</package>
